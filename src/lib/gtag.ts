// Google Analytics 配置
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID || '';

// 检查是否在生产环境且有有效的 GA ID
export const isProduction = process.env.NODE_ENV === 'production';
export const isGAEnabled = isProduction && GA_TRACKING_ID;

// 页面浏览事件
export const pageview = (url: string) => {
  if (!isGAEnabled || typeof window === 'undefined') return;
  
  window.gtag('config', GA_TRACKING_ID, {
    page_location: url,
  });
};

// 自定义事件
export const event = ({
  action,
  category,
  label,
  value,
}: {
  action: string;
  category: string;
  label?: string;
  value?: number;
}) => {
  if (!isGAEnabled || typeof window === 'undefined') return;
  
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};

// 常用事件函数
export const trackEvent = {
  // 文章阅读
  readArticle: (articleTitle: string, articleSlug: string) => {
    event({
      action: 'read_article',
      category: 'engagement',
      label: `${articleTitle} (${articleSlug})`,
    });
  },

  // 搜索
  search: (searchTerm: string, resultCount: number) => {
    event({
      action: 'search',
      category: 'engagement',
      label: searchTerm,
      value: resultCount,
    });
  },

  // 联系表单提交
  contactForm: (formType: string) => {
    event({
      action: 'form_submit',
      category: 'contact',
      label: formType,
    });
  },

  // 外部链接点击
  externalLink: (url: string, linkText: string) => {
    event({
      action: 'click_external_link',
      category: 'engagement',
      label: `${linkText} -> ${url}`,
    });
  },

  // 微信公众号点击
  wechatClick: () => {
    event({
      action: 'wechat_qr_click',
      category: 'social',
      label: 'floating_wechat',
    });
  },
};

// 声明全局 gtag 函数
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event',
      targetId: string,
      config?: Record<string, unknown>
    ) => void;
  }
}
