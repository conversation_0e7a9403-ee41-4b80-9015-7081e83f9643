/**
 * 计算文章阅读时长的工具函数
 */

/**
 * 计算阅读时长（分钟）
 * @param content 文章内容（Markdown 或 HTML）
 * @returns 阅读时长（分钟）
 */
export function calculateReadingTime(content: string): number {
  if (!content) return 1;

  // 移除 HTML 标签
  const htmlStripped = content.replace(/<[^>]*>/g, '');
  
  // 移除 Markdown 语法
  const markdownStripped = htmlStripped
    .replace(/#{1,6}\s+/g, '') // 标题
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 粗体
    .replace(/\*([^*]+)\*/g, '$1') // 斜体
    .replace(/`([^`]+)`/g, '$1') // 行内代码
    .replace(/```[\s\S]*?```/g, '') // 代码块
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1') // 图片
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 链接
    .replace(/^\s*[-*+]\s+/gm, '') // 列表项
    .replace(/^\s*\d+\.\s+/gm, '') // 有序列表
    .replace(/^\s*>\s+/gm, '') // 引用
    .replace(/\n{2,}/g, '\n') // 多个换行符
    .trim();

  // 计算字符数（中文按字符计算，英文按单词计算）
  const chineseChars = (markdownStripped.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = markdownStripped
    .replace(/[\u4e00-\u9fff]/g, '') // 移除中文字符
    .split(/\s+/)
    .filter(word => word.length > 0).length;

  // 中文阅读速度：400字/分钟
  // 英文阅读速度：200词/分钟
  const chineseReadingTime = chineseChars / 400;
  const englishReadingTime = englishWords / 200;
  
  const totalReadingTime = chineseReadingTime + englishReadingTime;
  
  // 至少1分钟，向上取整
  return Math.max(1, Math.ceil(totalReadingTime));
}

/**
 * 格式化阅读时长显示
 * @param minutes 阅读时长（分钟）
 * @param format 显示格式
 * @param translations 翻译文本（可选，用于多语言支持）
 * @returns 格式化后的字符串
 */
export function formatReadingTime(
  minutes: number,
  format: 'full' | 'short' | 'minimal' = 'full',
  translations?: {
    full: string;
    short: string;
    minimal: string;
  }
): string {
  // 如果提供了翻译，使用翻译文本
  if (translations) {
    switch (format) {
      case 'short':
        return `${minutes} ${translations.short}`;
      case 'minimal':
        return `${minutes}${translations.minimal}`;
      case 'full':
      default:
        return `${minutes} ${translations.full}`;
    }
  }

  // 默认使用中文（向后兼容）
  switch (format) {
    case 'short':
      return `${minutes} min`;
    case 'minimal':
      return `${minutes}m`;
    case 'full':
    default:
      return `${minutes} 分钟阅读`;
  }
}

/**
 * 从文章内容估算阅读时长并格式化
 * @param content 文章内容
 * @param format 显示格式
 * @param translations 翻译文本（可选，用于多语言支持）
 * @returns 格式化的阅读时长字符串
 */
export function getReadingTimeText(
  content: string,
  format: 'full' | 'short' | 'minimal' = 'full',
  translations?: {
    full: string;
    short: string;
    minimal: string;
  }
): string {
  const minutes = calculateReadingTime(content);
  return formatReadingTime(minutes, format, translations);
}