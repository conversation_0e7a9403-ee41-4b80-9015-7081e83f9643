import { Post } from "@/interfaces/post";
import fs from "fs";
import matter from "gray-matter";
import { join } from "path";
import { defaultLocale } from "@/i18n";

const postsDirectory = join(process.cwd(), "_posts");

export function getPostSlugs(locale: string = defaultLocale) {
  const localePostsDirectory = join(postsDirectory, locale);
  if (!fs.existsSync(localePostsDirectory)) {
    // 如果指定语言目录不存在，回退到默认语言
    const defaultPostsDirectory = join(postsDirectory, defaultLocale);
    return fs.existsSync(defaultPostsDirectory) ? fs.readdirSync(defaultPostsDirectory) : [];
  }
  return fs.readdirSync(localePostsDirectory);
}

export function getPostBySlug(slug: string, locale: string = defaultLocale) {
  const realSlug = slug.replace(/\.md$/, "");
  const localePostsDirectory = join(postsDirectory, locale);
  const fullPath = join(localePostsDirectory, `${realSlug}.md`);
  
  // 如果指定语言的文章不存在，直接返回null（404）
  if (!fs.existsSync(fullPath)) {
    return null;
  }
  
  const fileContents = fs.readFileSync(fullPath, "utf8");
  const { data, content } = matter(fileContents);

  return { ...data, slug: realSlug, content } as Post;
}

export function getAllPosts(locale: string = defaultLocale): Post[] {
  const slugs = getPostSlugs(locale);
  const posts = slugs
    .map((slug) => getPostBySlug(slug, locale))
    .filter((post): post is Post => post !== null)
    // sort posts by date in descending order
    .sort((post1, post2) => (post1.date > post2.date ? -1 : 1));
  return posts;
}

// 获取所有精选文章
export function getFeaturedPosts(locale: string = defaultLocale): Post[] {
  const allPosts = getAllPosts(locale);
  return allPosts
    .filter(post => post.featured === true)
    .sort((a, b) => {
      // 按 featuredOrder 排序，如果没有则按日期排序
      const orderA = a.featuredOrder || 999;
      const orderB = b.featuredOrder || 999;
      if (orderA !== orderB) {
        return orderA - orderB;
      }
      return a.date > b.date ? -1 : 1;
    });
}

// 获取主要精选文章（首页显示用）
export function getPrimaryFeaturedPost(locale: string = defaultLocale): Post | null {
  const featuredPosts = getFeaturedPosts(locale);
  if (featuredPosts.length > 0) {
    return featuredPosts[0]; // 返回排序第一的精选文章
  }
  // 如果没有精选文章，返回null（不显示精选区域）
  return null;
}

// 获取推荐文章（除了主精选文章外的其他精选文章）
export function getRecommendedPosts(excludeSlug?: string, locale: string = defaultLocale): Post[] {
  const featuredPosts = getFeaturedPosts(locale);
  const filtered = excludeSlug 
    ? featuredPosts.filter(post => post.slug !== excludeSlug)
    : featuredPosts.slice(1); // 排除第一个主精选文章
  
  // 如果精选文章不够，用最新文章补充
  if (filtered.length < 12) {
    const allPosts = getAllPosts(locale);
    const nonFeatured = allPosts.filter(post => 
      post.featured !== true && (!excludeSlug || post.slug !== excludeSlug)
    );
    filtered.push(...nonFeatured.slice(0, 12 - filtered.length));
  }
  
  return filtered.slice(0, 12);
}
