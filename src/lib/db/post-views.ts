import { db } from '@/lib/database'

// 增加文章浏览次数
export async function incrementPostView(slug: string): Promise<number> {
  try {
    // 首先尝试更新现有记录
    const updateResult = await db.execute({
      sql: `UPDATE post_views
            SET view_count = view_count + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE slug = ?`,
      args: [slug]
    })

    // 如果没有更新任何行，说明记录不存在，需要插入新记录
    if (updateResult.rowsAffected === 0) {
      await db.execute({
        sql: `INSERT INTO post_views (slug, view_count)
              VALUES (?, 1)`,
        args: [slug]
      })
      return 1
    }

    // 获取更新后的浏览次数
    const result = await db.execute({
      sql: `SELECT view_count FROM post_views WHERE slug = ?`,
      args: [slug]
    })

    return result.rows[0]?.view_count as number || 0
  } catch (error) {
    console.error('Error incrementing post view:', error)
    return 0
  }
}

// 获取文章浏览次数
export async function getPostViewCount(slug: string): Promise<number> {
  try {
    const result = await db.execute({
      sql: `SELECT view_count FROM post_views WHERE slug = ?`,
      args: [slug]
    })

    return result.rows[0]?.view_count as number || 0
  } catch (error) {
    console.error('Error getting post view count:', error)
    return 0
  }
}

// 获取所有文章的浏览统计
export async function getAllPostViews(): Promise<{ slug: string; viewCount: number }[]> {
  try {
    const result = await db.execute(`
      SELECT slug, view_count as viewCount
      FROM post_views
      ORDER BY view_count DESC
    `)

    return result.rows.map(row => ({
      slug: row.slug as string,
      viewCount: row.viewCount as number
    }))
  } catch (error) {
    console.error('Error getting all post views:', error)
    return []
  }
}

// 批量获取多个文章的浏览次数
export async function getMultiplePostViews(slugs: string[]): Promise<{ slug: string; viewCount: number }[]> {
  try {
    if (slugs.length === 0) {
      return []
    }

    // 构建 IN 查询的占位符
    const placeholders = slugs.map(() => '?').join(',')

    const result = await db.execute({
      sql: `SELECT slug, view_count as viewCount
            FROM post_views
            WHERE slug IN (${placeholders})`,
      args: slugs
    })

    // 创建结果映射，确保所有请求的 slug 都有返回值
    const viewMap = new Map(
      result.rows.map(row => [row.slug as string, row.viewCount as number])
    )

    return slugs.map(slug => ({
      slug,
      viewCount: viewMap.get(slug) || 0
    }))
  } catch (error) {
    console.error('Error getting multiple post views:', error)
    return slugs.map(slug => ({ slug, viewCount: 0 }))
  }
}
