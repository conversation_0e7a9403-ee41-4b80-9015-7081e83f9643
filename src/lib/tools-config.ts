// 工具配置文件 - 统一管理所有在线工具
export interface Tool {
  id: string;
  titleKey: string;
  descriptionKey: string;
  icon: string;
  href: string;
  categoryKey: string;
  publishDate: string;
}

export const tools: Tool[] = [
  {
    id: "json-formatter",
    titleKey: "jsonFormatter.title",
    descriptionKey: "jsonFormatter.description",
    icon: "📋",
    href: "/tools/json-formatter",
    categoryKey: "dataProcessing",
    publishDate: "2023-12-28"
  },
  {
    id: "base64",
    titleKey: "base64.title",
    descriptionKey: "base64.description",
    icon: "🔐",
    href: "/tools/base64",
    categoryKey: "encoding",
    publishDate: "2023-12-25"
  },
  {
    id: "url-encode",
    titleKey: "urlEncode.title",
    descriptionKey: "urlEncode.description",
    icon: "🔗",
    href: "/tools/url-encode",
    categoryKey: "encoding",
    publishDate: "2023-12-22"
  },
  {
    id: "timestamp",
    titleKey: "timestamp.title",
    descriptionKey: "timestamp.description",
    icon: "⏰",
    href: "/tools/timestamp",
    categoryKey: "timeTools",
    publishDate: "2023-12-15"
  },
  {
    id: "regex-test",
    titleKey: "regexTest.title",
    descriptionKey: "regexTest.description",
    icon: "🔍",
    href: "/tools/regex-test",
    categoryKey: "textProcessing",
    publishDate: "2023-12-01"
  },
  {
    id: "currency-converter",
    titleKey: "currencyConverter.title",
    descriptionKey: "currencyConverter.description",
    icon: "💱",
    href: "/tools/currency-converter",
    categoryKey: "financeTools",
    publishDate: "2024-01-12"
  },
  {
    id: "color-picker",
    titleKey: "colorPicker.title",
    descriptionKey: "colorPicker.description",
    icon: "🎨",
    href: "/tools/color-picker",
    categoryKey: "designTools",
    publishDate: "2024-01-03"
  },
  {
    id: "qr-generator",
    titleKey: "qrGenerator.title",
    descriptionKey: "qrGenerator.description",
    icon: "📱",
    href: "/tools/qr-generator",
    categoryKey: "utilityTools",
    publishDate: "2024-01-10"
  },
  {
    id: "password-generator",
    titleKey: "passwordGenerator.title",
    descriptionKey: "passwordGenerator.description",
    icon: "🔒",
    href: "/tools/password-generator",
    categoryKey: "securityTools",
    publishDate: "2024-01-05"
  },
  {
    id: "id-card-validator",
    titleKey: "idCardValidator.title",
    descriptionKey: "idCardValidator.description",
    icon: "🆔",
    href: "/tools/id-card-validator",
    categoryKey: "utilityTools",
    publishDate: "2023-11-20"
  },
  {
    id: "id-card-generator",
    titleKey: "idCardGenerator.title",
    descriptionKey: "idCardGenerator.description",
    icon: "🆔",
    href: "/tools/id-card-generator",
    categoryKey: "utilityTools",
    publishDate: "2023-11-15"
  },
  {
    id: "ai-git-commit",
    titleKey: "aiGitCommit.title",
    descriptionKey: "aiGitCommit.description",
    icon: "🤖",
    href: "/tools/ai-git-commit",
    categoryKey: "devTools",
    publishDate: "2025-01-15"
  },
  {
    id: "ip-lookup",
    titleKey: "ipLookup.title",
    descriptionKey: "ipLookup.description",
    icon: "🌐",
    href: "/tools/ip-lookup",
    categoryKey: "networkTools",
    publishDate: "2023-11-15"
  },
  {
    id: "cron-parser",
    titleKey: "cronParser.title",
    descriptionKey: "cronParser.description",
    icon: "⏱️",
    href: "/tools/cron-parser",
    categoryKey: "devTools",
    publishDate: "2023-11-10"
  },
  {
    id: "rmb-converter",
    titleKey: "rmbConverter.title",
    descriptionKey: "rmbConverter.description",
    icon: "💰",
    href: "/tools/rmb-converter",
    categoryKey: "financeTools",
    publishDate: "2023-11-05"
  },
  {
    id: "unit-converter",
    titleKey: "unitConverter.title",
    descriptionKey: "unitConverter.description",
    icon: "📏",
    href: "/tools/unit-converter",
    categoryKey: "utilityTools",
    publishDate: "2023-10-30"
  },
  {
    id: "novel-name-generator",
    titleKey: "novelNameGenerator.title",
    descriptionKey: "novelNameGenerator.description",
    icon: "✨",
    href: "/tools/novel-name-generator",
    categoryKey: "creativeTools",
    publishDate: "2023-10-25"
  },
  {
    id: "mysql-password-generator",
    titleKey: "mysqlPasswordGenerator.title",
    descriptionKey: "mysqlPasswordGenerator.description",
    icon: "🔐",
    href: "/tools/mysql-password-generator",
    categoryKey: "securityTools",
    publishDate: "2023-10-20"
  },
  {
    id: "mysql-hash-generator",
    titleKey: "mysqlHashGenerator.title",
    descriptionKey: "mysqlHashGenerator.description",
    icon: "🔒",
    href: "/tools/mysql-hash-generator",
    categoryKey: "securityTools",
    publishDate: "2023-10-15"
  },
  {
    id: "wechat-auth",
    titleKey: "wechatAuth.title",
    descriptionKey: "wechatAuth.description",
    icon: "🔗",
    href: "/tools/wechat-auth",
    categoryKey: "devTools",
    publishDate: "2024-01-15"
  }
].sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime());

// 获取所有工具的函数
export function getAllTools(): Tool[] {
  return tools;
}

// 获取工具分类的函数
export function getToolCategories(): string[] {
  return Array.from(new Set(tools.map(tool => tool.categoryKey)));
}