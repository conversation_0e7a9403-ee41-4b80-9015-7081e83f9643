import { getRequestConfig } from 'next-intl/server';

// 支持的语言列表
export const locales = ['zh', 'en'] as const;
export type Locale = (typeof locales)[number];

// 默认语言
export const defaultLocale: Locale = 'zh';

// 动态加载拆分后的翻译文件
async function loadMessages(locale: string) {
  try {
    const messages: any = {};

    // 手动导入所有已知的翻译文件
    const imports = [
      // Common files
      { path: 'common', module: () => import(`../messages/${locale}/common.json`) },
      { path: 'components', module: () => import(`../messages/${locale}/components.json`) },
      { path: 'site', module: () => import(`../messages/${locale}/site.json`) },
      { path: 'nav', module: () => import(`../messages/${locale}/nav.json`) },
      { path: 'footer', module: () => import(`../messages/${locale}/footer.json`) },

      // Page files
      { path: 'pages', module: () => import(`../messages/${locale}/pages.json`) },
      { path: 'pages.home', module: () => import(`../messages/${locale}/pages/home.json`) },
      { path: 'pages.posts', module: () => import(`../messages/${locale}/pages/posts.json`) },
      { path: 'pages.about', module: () => import(`../messages/${locale}/pages/about.json`) },
      { path: 'pages.terms', module: () => import(`../messages/${locale}/pages/terms.json`) },
      { path: 'pages.privacy', module: () => import(`../messages/${locale}/pages/privacy.json`) },
      { path: 'pages.adsPolicy', module: () => import(`../messages/${locale}/pages/ads-policy.json`) },

      // Tools files
      { path: 'pages.tools', module: () => import(`../messages/${locale}/pages/tools/index.json`) },
      { path: 'pages.tools.tools.qrGenerator', module: () => import(`../messages/${locale}/pages/tools/tools/qrGenerator.json`) },
      { path: 'pages.tools.tools.base64', module: () => import(`../messages/${locale}/pages/tools/tools/base64.json`) },
      { path: 'pages.tools.tools.ipLookup', module: () => import(`../messages/${locale}/pages/tools/tools/ipLookup.json`) },
      { path: 'pages.tools.tools.colorPicker', module: () => import(`../messages/${locale}/pages/tools/tools/colorPicker.json`) },
      { path: 'pages.tools.tools.timestamp', module: () => import(`../messages/${locale}/pages/tools/tools/timestamp.json`) },
      { path: 'pages.tools.tools.jsonFormatter', module: () => import(`../messages/${locale}/pages/tools/tools/jsonFormatter.json`) },
      { path: 'pages.tools.tools.urlEncode', module: () => import(`../messages/${locale}/pages/tools/tools/urlEncode.json`) },
      { path: 'pages.tools.tools.jsonFormatter', module: () => import(`../messages/${locale}/pages/tools/tools/jsonFormatter.json`) },
      { path: 'pages.tools.tools.urlEncode', module: () => import(`../messages/${locale}/pages/tools/tools/urlEncode.json`) },
      { path: 'pages.tools.tools.regexTest', module: () => import(`../messages/${locale}/pages/tools/tools/regexTest.json`) },
      { path: 'pages.tools.tools.passwordGenerator', module: () => import(`../messages/${locale}/pages/tools/tools/passwordGenerator.json`) },
      { path: 'pages.tools.tools.currencyConverter', module: () => import(`../messages/${locale}/pages/tools/tools/currencyConverter.json`) },
      { path: 'pages.tools.tools.idCardValidator', module: () => import(`../messages/${locale}/pages/tools/tools/idCardValidator.json`) },
      { path: 'pages.tools.tools.idCardGenerator', module: () => import(`../messages/${locale}/pages/tools/tools/idCardGenerator.json`) },
      { path: 'pages.tools.tools.cronParser', module: () => import(`../messages/${locale}/pages/tools/tools/cronParser.json`) },
      { path: 'pages.tools.tools.rmbConverter', module: () => import(`../messages/${locale}/pages/tools/tools/rmbConverter.json`) },
      { path: 'pages.tools.tools.unitConverter', module: () => import(`../messages/${locale}/pages/tools/tools/unitConverter.json`) },
      { path: 'pages.tools.tools.novelNameGenerator', module: () => import(`../messages/${locale}/pages/tools/tools/novelNameGenerator.json`) },
      { path: 'pages.tools.tools.mysqlPasswordGenerator', module: () => import(`../messages/${locale}/pages/tools/tools/mysqlPasswordGenerator.json`) },
      { path: 'pages.tools.tools.mysqlHashGenerator', module: () => import(`../messages/${locale}/pages/tools/tools/mysqlHashGenerator.json`) },
      { path: 'pages.tools.tools.wechatAuth', module: () => import(`../messages/${locale}/pages/tools/tools/wechatAuth.json`) },
      { path: 'pages.tools.tools.aiGitCommit', module: () => import(`../messages/${locale}/pages/tools/tools/aiGitCommit.json`) },
    ];

    for (const { path, module } of imports) {
      try {
        const content = await module();

        // 根据路径构建嵌套对象
        const keys = path.split('.');
        let current = messages;

        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {};
          }
          current = current[keys[i]];
        }

        current[keys[keys.length - 1]] = content.default || content;
      } catch (error) {
        console.warn(`Failed to load ${path} for locale ${locale}:`, error);
      }
    }
    return messages;
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);
    return {};
  }
}

export default getRequestConfig(async ({ requestLocale }) => {
  // 获取请求的locale
  let locale = await requestLocale;

  // 如果locale为空或不支持，使用默认语言
  if (!locale || !locales.includes(locale as Locale)) {
    locale = defaultLocale;
  }

  return {
    locale,
    messages: await loadMessages(locale)
  };
});