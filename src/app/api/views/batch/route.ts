import { NextRequest, NextResponse } from 'next/server';
import { getMultiplePostViews } from '@/lib/db/post-views';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { slugs } = body;

    if (!Array.isArray(slugs)) {
      return NextResponse.json(
        { error: 'Slugs must be an array' },
        { status: 400 }
      );
    }

    // 从数据库批量获取访问次数
    const postViews = await getMultiplePostViews(slugs);

    const results = postViews.map(pv => ({
      slug: pv.slug,
      views: pv.viewCount
    }));

    return NextResponse.json({
      results,
      success: true
    });
  } catch (error) {
    console.error('Error getting multiple view counts:', error);
    return NextResponse.json(
      { error: 'Failed to get view counts' },
      { status: 500 }
    );
  }
}
