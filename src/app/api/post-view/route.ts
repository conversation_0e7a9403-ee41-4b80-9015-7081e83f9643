import { NextRequest, NextResponse } from 'next/server'
import { incrementPostView, getPostViewCount } from '@/lib/db/post-views'
import { z } from 'zod'

const postViewSchema = z.object({
  slug: z.string().min(1, '文章 slug 不能为空')
})

// 增加文章浏览次数
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { slug } = postViewSchema.parse(body)

    const viewCount = await incrementPostView(slug)

    return NextResponse.json({
      success: true,
      data: { slug, viewCount }
    })
  } catch (error) {
    console.error('Post view increment error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0].message
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: '记录浏览次数失败'
    }, { status: 500 })
  }
}

// 获取文章浏览次数
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (!slug) {
      return NextResponse.json({
        success: false,
        message: '缺少文章 slug 参数'
      }, { status: 400 })
    }

    const viewCount = await getPostViewCount(slug)

    return NextResponse.json({
      success: true,
      data: { slug, viewCount }
    })
  } catch (error) {
    console.error('Get post view error:', error)

    return NextResponse.json({
      success: false,
      message: '获取浏览次数失败'
    }, { status: 500 })
  }
}
