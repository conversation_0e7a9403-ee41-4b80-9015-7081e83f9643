/* 自定义字体和基础样式 */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

/* 代码高亮样式 */
@import "prismjs/themes/prism-tomorrow.css";
@import "prismjs/plugins/line-numbers/prism-line-numbers.css";

@import "tailwindcss";

/* Mermaid 相关样式 */
pre.mermaid-processed,
pre:has(code.language-mermaid) {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  left: -9999px !important;
}

/* 强制隐藏所有包含mermaid的pre元素 */
pre code.language-mermaid {
  display: none !important;
}

pre:has(code.language-mermaid) {
  display: none !important;
}

/* Tailwind CSS 4.x 配置 */
@theme {
  /* 主色调 */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #6366f1;
  --color-primary-600: #4f46e5;
  --color-primary-700: #4338ca;
  --color-primary-800: #3730a3;
  --color-primary-900: #312e81;

  /* 辅助色 */
  --color-secondary-50: #fdf2f8;
  --color-secondary-100: #fce7f3;
  --color-secondary-200: #fbcfe8;
  --color-secondary-300: #f9a8d4;
  --color-secondary-400: #f472b6;
  --color-secondary-500: #ec4899;
  --color-secondary-600: #db2777;
  --color-secondary-700: #be185d;
  --color-secondary-800: #9d174d;
  --color-secondary-900: #831843;

  /* 语义色 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* 兼容性颜色 */
  --color-accent-1: #fafafa;
  --color-accent-2: #eaeaea;
  --color-accent-7: #333;
  --color-cyan: #79ffe1;

  /* 间距 */
  --spacing-18: 4.5rem;
  --spacing-28: 7rem;

  /* 动画时长 */
  --animate-duration-slow: 3s;
  --animate-duration-slower: 8s;
}

/* 自定义动画 */
@keyframes gradient {
  0%,
  100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* 动画类 */
.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-spin-slow {
  animation: spin var(--animate-duration-slower) linear infinite;
}

/* 悬停效果 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 渐变边框 */
.gradient-border {
  position: relative;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
  background-size: 300% 300%;
  animation: gradient 3s ease infinite;
}

.gradient-border::before {
  content: "";
  position: absolute;
  inset: 1px;
  background: inherit;
  border-radius: inherit;
  background: var(--color-slate-900);
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 发光按钮 */
.btn-glow {
  position: relative;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

.btn-glow:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

:root {
  --spacing-88: 22rem;
  --spacing-96: 24rem;

  /* 字间距 */
  --letter-spacing-tighter: -0.04em;
  --letter-spacing-wider: 0.05em;

  /* 字体大小 */
  --font-size-5xl: 2.5rem;
  --font-size-6xl: 2.75rem;
  --font-size-7xl: 4.5rem;
  --font-size-8xl: 6.25rem;
  --font-size-9xl: 8rem;

  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(99, 102, 241, 0.4);

  /* 边框半径 */
  --radius-4xl: 2rem;

  /* 背景渐变 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
  }

  /* 防止所有元素产生横向滚动 */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* 确保容器不会超出视口 */
  .container {
    max-width: 100%;
    overflow-x: hidden;
  }

  /* 代码字体 */
  code,
  pre {
    font-family: "JetBrains Mono", "Fira Code", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  }

  /* 选择文本样式 */
  ::selection {
    background-color: #dbeafe;
    color: #1e40af;
  }

  ::-moz-selection {
    background-color: #dbeafe;
    color: #1e40af;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: #f5f5f5;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #d4d4d4;
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #a3a3a3;
  }
}

@layer components {
  /* 按钮组件 */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s;
    outline: none;
  }

  .btn:focus {
    box-shadow: 0 0 0 2px var(--color-primary-500);
  }

  .btn-primary {
    background-color: var(--color-primary-600);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-primary:hover {
    background-color: var(--color-primary-700);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .btn-primary:focus {
    box-shadow: 0 0 0 2px var(--color-primary-500);
  }

  .btn-secondary {
    background-color: white;
    color: #525252;
    border: 1px solid #d4d4d4;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .btn-secondary:hover {
    background-color: #fafafa;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary:focus {
    box-shadow: 0 0 0 2px var(--color-primary-500);
  }

  .btn-ghost {
    color: #525252;
  }

  .btn-ghost:hover {
    color: #171717;
    background-color: #f5f5f5;
  }

  .btn-ghost:focus {
    box-shadow: 0 0 0 2px var(--color-primary-500);
  }

  /* 新的阅读全文按钮样式 */
  .btn-read-more {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  .btn-read-more::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-read-more:hover::before {
    left: 100%;
  }

  .btn-read-more:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    background: linear-gradient(135deg, var(--color-primary-700) 0%, var(--color-primary-800) 100%);
  }

  .btn-read-more:active {
    transform: translateY(0);
  }

  /* 磁性按钮效果 */
  .btn-magnetic {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 50px;
    background: var(--color-primary-600);
    color: white;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .btn-magnetic::before {
    content: "";
    position: absolute;
    inset: -2px;
    border-radius: 50px;
    background: linear-gradient(45deg, var(--color-primary-400), var(--color-secondary-500), var(--color-primary-600));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-magnetic:hover::before {
    opacity: 1;
  }

  .btn-magnetic:hover {
    background: var(--color-primary-700);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
  }

  /* 发光按钮 */
  .btn-glow {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 0.5rem;
    background: var(--color-primary-600);
    color: white;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .btn-glow::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 0.5rem;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-glow:hover {
    background: var(--color-primary-700);
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
    transform: translateY(-1px);
  }

  .btn-glow:hover::after {
    opacity: 1;
  }

  /* 渐变边框按钮 */
  .btn-gradient-border {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 0.75rem;
    background: white;
    color: var(--color-primary-700);
    border: none;
    transition: all 0.3s ease;
  }

  .btn-gradient-border::before {
    content: "";
    position: absolute;
    inset: -2px;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500), var(--color-primary-600));
    z-index: -1;
  }

  .btn-gradient-border:hover {
    color: white;
    background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  }

  /* 3D 按钮效果 */
  .btn-3d {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 0.5rem;
    background: linear-gradient(145deg, var(--color-primary-500), var(--color-primary-700));
    color: white;
    border: none;
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
  }

  .btn-3d:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .btn-3d:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* 卡片组件 */
  .card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    border: 1px solid #e5e5e5;
  }

  .card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .card-hover {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    border: 1px solid #e5e5e5;
  }

  .card-hover:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  /* 渐变文本 */
  .text-gradient {
    background: linear-gradient(to right, #4f46e5, #db2777);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .text-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  /* 玻璃态效果 */
  .glass {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  /* 响应式容器 */
  .container-custom {
    max-width: 80rem;
    margin: 0 auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-custom {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
}

@layer utilities {
  /* 文本渐变工具类 */
  .bg-gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 隐藏滚动条但保持功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 自定义阴影 */
  .shadow-elegant {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .shadow-elegant-lg {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  /* 特色文章专用阴影 */
  .shadow-featured {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .shadow-featured-lg {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.12);
  }

  /* 发光效果 */
  .glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .glow-lg {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.4);
  }

  /* 文本渐变 */
  .text-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 网格背景 */
  .bg-grid-slate-100\/\[0\.02\] {
    background-image: radial-gradient(circle, #f1f5f9 1px, transparent 1px);
  }

  .bg-grid-slate-700\/\[0\.05\] {
    background-image: radial-gradient(circle, #334155 1px, transparent 1px);
  }

  /* 行截断 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 动画关键帧 */
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes pulse-slow {
    0%,
    100% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.8;
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse-slow 4s ease-in-out infinite;
  }

  /* 动画延迟 */
  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  .animation-delay-800 {
    animation-delay: 800ms;
  }

  .animation-delay-1000 {
    animation-delay: 1000ms;
  }

  .animation-delay-2000 {
    animation-delay: 2000ms;
  }

  .animation-delay-3000 {
    animation-delay: 3000ms;
  }

  .animation-delay-4000 {
    animation-delay: 4000ms;
  }

  /* 文本截断 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
  }

  /* 动画延迟 */
  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* 长宽比 */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  /* 加载动画 */
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* 平滑滚动 */
  @media (prefers-reduced-motion: no-preference) {
    html {
      scroll-behavior: smooth;
    }
  }

  /* 焦点可见性 */
  .focus-visible:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
  }

  /* 响应式文本 */
  .text-responsive {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  .text-responsive-xl {
    font-size: clamp(2rem, 6vw, 5rem);
  }
}

/* 现代化动画效果 */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gradient-x {
  0%,
  100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* 流动背景动画 */
@keyframes float-slow {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

@keyframes float-reverse {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(-30px, 30px) rotate(-120deg);
  }
  66% {
    transform: translate(20px, -20px) rotate(-240deg);
  }
}

@keyframes float-diagonal {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
  }
  50% {
    transform: translate(-40px, -40px) scale(1.1);
  }
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(15px, -10px) rotate(90deg);
  }
  50% {
    transform: translate(10px, 15px) rotate(180deg);
  }
  75% {
    transform: translate(-10px, 5px) rotate(270deg);
  }
}

@keyframes float-slow-reverse {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(25px, -35px) rotate(180deg);
  }
}

@keyframes flow-horizontal {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes flow-horizontal-reverse {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes flow-vertical {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes flow-vertical-reverse {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes particle-float {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(20px, -30px) scale(1.2);
    opacity: 1;
  }
}

@keyframes particle-float-delay {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translate(-25px, 20px) scale(0.8);
    opacity: 0.8;
  }
}

@keyframes particle-float-slow {
  0%,
  100% {
    transform: translate(0, 0);
    opacity: 0.5;
  }
  50% {
    transform: translate(15px, -20px);
    opacity: 1;
  }
}

@keyframes particle-float-reverse {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-20px, -15px) scale(1.1);
    opacity: 0.9;
  }
}

@keyframes particle-float-gentle {
  0%,
  100% {
    transform: translate(0, 0);
    opacity: 0.4;
  }
  50% {
    transform: translate(10px, 15px);
    opacity: 0.7;
  }
}

/* 自定义动画类 */
.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 流动背景动画类 */
.animate-float-slow {
  animation: float-slow 20s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 25s ease-in-out infinite;
}

.animate-float-diagonal {
  animation: float-diagonal 18s ease-in-out infinite;
}

.animate-float-gentle {
  animation: float-gentle 22s ease-in-out infinite;
}

.animate-float-slow-reverse {
  animation: float-slow-reverse 24s ease-in-out infinite;
}

.animate-flow-horizontal {
  animation: flow-horizontal 8s linear infinite;
}

.animate-flow-horizontal-reverse {
  animation: flow-horizontal-reverse 10s linear infinite;
}

.animate-flow-vertical {
  animation: flow-vertical 12s linear infinite;
}

.animate-flow-vertical-reverse {
  animation: flow-vertical-reverse 14s linear infinite;
}

.animate-particle-float {
  animation: particle-float 6s ease-in-out infinite;
}

.animate-particle-float-delay {
  animation: particle-float-delay 8s ease-in-out infinite 2s;
}

.animate-particle-float-slow {
  animation: particle-float-slow 10s ease-in-out infinite;
}

.animate-particle-float-reverse {
  animation: particle-float-reverse 7s ease-in-out infinite 1s;
}

.animate-particle-float-gentle {
  animation: particle-float-gentle 9s ease-in-out infinite 3s;
}

/* 移动设备性能优化 */
@media (max-width: 768px) {
  .animate-float-slow,
  .animate-float-reverse,
  .animate-float-diagonal,
  .animate-float-gentle,
  .animate-float-slow-reverse {
    animation-duration: 30s; /* 减慢动画速度以提高性能 */
  }

  .animate-flow-horizontal,
  .animate-flow-horizontal-reverse,
  .animate-flow-vertical,
  .animate-flow-vertical-reverse {
    animation-duration: 15s; /* 减慢流动效果 */
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .animate-float-slow,
  .animate-float-reverse,
  .animate-float-diagonal,
  .animate-float-gentle,
  .animate-float-slow-reverse,
  .animate-flow-horizontal,
  .animate-flow-horizontal-reverse,
  .animate-flow-vertical,
  .animate-flow-vertical-reverse,
  .animate-particle-float,
  .animate-particle-float-delay,
  .animate-particle-float-slow,
  .animate-particle-float-reverse,
  .animate-particle-float-gentle {
    animation: none;
  }
}

/* 玻璃态效果 */
.glass {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

.glass-dark {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(17, 25, 40, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

/* 现代化按钮效果 */
.btn-modern {
  position: relative;
  overflow: hidden;
  transform: perspective(1px) translateZ(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* 文字渐变效果 */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 卡片悬浮效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
