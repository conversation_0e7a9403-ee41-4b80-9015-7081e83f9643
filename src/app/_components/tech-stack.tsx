export function TechStack() {
  const techCategories = [
    {
      title: "前端开发",
      icon: "🎨",
      color: "from-blue-500 to-cyan-500",
      techs: [
        { name: "React", level: 95, color: "bg-blue-500" },
        { name: "Next.js", level: 90, color: "bg-slate-700" },
        { name: "TypeScript", level: 88, color: "bg-blue-600" },
        { name: "Tailwind CSS", level: 92, color: "bg-cyan-500" }
      ]
    },
    {
      title: "后端开发",
      icon: "⚙️",
      color: "from-green-500 to-emerald-500",
      techs: [
        { name: "Node.js", level: 85, color: "bg-green-600" },
        { name: "Python", level: 80, color: "bg-yellow-500" },
        { name: "PostgreSQL", level: 75, color: "bg-blue-700" },
        { name: "Redis", level: 70, color: "bg-red-500" }
      ]
    },
    {
      title: "工具链",
      icon: "🛠️",
      color: "from-purple-500 to-pink-500",
      techs: [
        { name: "Git", level: 90, color: "bg-orange-500" },
        { name: "Docker", level: 75, color: "bg-blue-600" },
        { name: "Vercel", level: 85, color: "bg-slate-800" },
        { name: "VS Code", level: 95, color: "bg-blue-500" }
      ]
    }
  ];

  return (
    <section className="py-24 md:py-32 relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200/20 to-purple-200/20 dark:from-blue-800/10 dark:to-purple-800/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-green-200/20 to-cyan-200/20 dark:from-green-800/10 dark:to-cyan-800/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-green-100 to-cyan-100 dark:from-green-900/30 dark:to-cyan-900/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium mb-4">
              🚀 技术栈
            </span>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white mb-6">
              我的<span className="text-gradient-primary">技术</span>工具箱
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed">
              这些是我在日常开发中使用的技术栈，每一项都经过实战检验，能够帮助构建高质量的应用
            </p>
          </div>

          {/* 技术分类 */}
          <div className="grid lg:grid-cols-3 gap-8">
            {techCategories.map((category, categoryIndex) => (
              <div
                key={category.title}
                className="group bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-slate-200/50 dark:border-slate-700/50"
                style={{ animationDelay: `${categoryIndex * 200}ms` }}
              >
                {/* 分类标题 */}
                <div className="flex items-center gap-4 mb-8">
                  <div className={`w-12 h-12 bg-gradient-to-br ${category.color} rounded-xl flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300`}>
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                    {category.title}
                  </h3>
                </div>

                {/* 技术列表 */}
                <div className="space-y-6">
                  {category.techs.map((tech, techIndex) => (
                    <div key={tech.name} className="group/tech">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-slate-700 dark:text-slate-300">
                          {tech.name}
                        </span>
                        <span className="text-sm text-slate-500 dark:text-slate-400">
                          {tech.level}%
                        </span>
                      </div>
                      
                      {/* 进度条 */}
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden">
                        <div
                          className={`h-full ${tech.color} rounded-full transition-all duration-1000 ease-out group-hover/tech:animate-pulse`}
                          style={{
                            width: `${tech.level}%`,
                            animationDelay: `${(categoryIndex * 4 + techIndex) * 100}ms`
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* 学习理念 */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl p-8 md:p-12 border border-slate-200/50 dark:border-slate-600/50">
              <div className="max-w-3xl mx-auto">
                <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-6">
                  持续学习，永不止步
                </h3>
                <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed mb-8">
                  技术在不断发展，我也在不断学习新的技术和工具。这个技术栈会随着我的成长而不断更新，
                  每一次学习都是为了能够创造更好的产品，解决更复杂的问题。
                </p>
                
                {/* 学习中的技术 */}
                <div className="flex flex-wrap justify-center gap-3">
                  {['Rust', 'Go', 'WebAssembly', 'Three.js', 'GraphQL'].map((tech) => (
                    <span
                      key={tech}
                      className="px-4 py-2 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-sm font-medium border border-yellow-200/50 dark:border-yellow-700/50"
                    >
                      🌱 {tech}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
