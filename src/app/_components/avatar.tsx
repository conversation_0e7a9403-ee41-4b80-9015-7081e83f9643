import Image from "next/image";

type Props = {
  name: string;
  picture: string;
};

const Avatar = ({ name }: Props) => {
  return (
    <div className="flex items-center">
      <div className="relative w-10 h-10 mr-3">
        <Image
          src="/images/avatar1.jpg"
          alt={name}
          fill
          className="rounded-full object-cover ring-2 ring-slate-200 dark:ring-slate-700"
        />
      </div>
      <div className="text-lg font-semibold text-slate-900 dark:text-white">{name}</div>
    </div>
  );
};

export default Avatar;
