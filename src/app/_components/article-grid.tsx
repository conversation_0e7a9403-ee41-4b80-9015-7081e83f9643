import Link from "next/link";
import Image from "next/image";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { type Author } from "@/interfaces/author";
import CoverImage from "./cover-image";
import LocalizedDateFormatter from "./localized-date-formatter";
import { ReadingTime } from "./reading-time";

type Post = {
  slug: string;
  title: string;
  date: string;
  coverImage: string;
  author: Author;
  excerpt: string;
  content: string;
};

type Props = {
  posts: Post[];
  title: string;
  description: string;
};

export function ArticleGrid({ posts, title, description }: Props) {
  const t = useTranslations('common');
  const locale = useLocale();
  
  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };
  
  return (
    <section className="py-8 md:py-12">
      <div className="mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
              {title}
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              {description}
            </p>
          </div>

          {/* 文章网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-5 px-4 py-6">
            {posts.map((post, index) => (
              <article
                key={post.slug}
                className="group bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 border border-slate-200/50 dark:border-slate-700/50 hover:border-blue-300/50 dark:hover:border-blue-600/50 hover:-translate-y-1"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {/* 封面图片 */}
                <div className="relative h-40 sm:h-36 md:h-32 lg:h-36 overflow-hidden rounded-t-lg">
                  <CoverImage
                    slug={post.slug}
                    title={post.title}
                    src={post.coverImage}
                    className="rounded-t-lg"
                  />

                  {/* 渐变遮罩 */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* 新文章标签 */}
                  {index < 2 && (
                    <div className="absolute top-2 right-2">
                      <span className="px-1.5 py-0.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-medium rounded-full shadow-md">
                        NEW
                      </span>
                    </div>
                  )}
                </div>

                {/* 内容区域 */}
                <div className="p-3 md:p-4">
                  {/* 文章元信息 */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-xs text-slate-500 dark:text-slate-400">
                      <LocalizedDateFormatter dateString={post.date} />
                    </span>
                    <ReadingTime
                      content={post.content}
                      format="full"
                      className="text-xs text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded-full"
                    />
                  </div>

                  {/* 标题 */}
                  <h3 className="text-sm md:text-base lg:text-lg font-bold leading-tight mb-2 text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    <Link href={getLocalizedHref(`/posts/${post.slug}`)} className="line-clamp-2">
                      {post.title}
                    </Link>
                  </h3>

                  {/* 摘要 */}
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed mb-3 line-clamp-2 text-xs md:text-sm">
                    {post.excerpt}
                  </p>

                  {/* 底部信息 */}
                  <div className="flex items-center justify-between">
                    {/* 作者信息 */}
                    <div className="flex items-center gap-1.5">
                      <div className="relative w-5 h-5">
                        <Image
                          src="/images/avatar1.jpg"
                          alt={post.author.name}
                          fill
                          className="rounded-full object-cover"
                        />
                      </div>
                      <span className="text-xs text-slate-600 dark:text-slate-300">
                        {post.author.name}
                      </span>
                    </div>

                    {/* 阅读更多 */}
                    <Link
                      href={getLocalizedHref(`/posts/${post.slug}`)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-xs font-medium flex items-center gap-1 group/link"
                    >
                      {t('read')}
                      <svg className="w-3 h-3 transform group-hover/link:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </Link>
                  </div>
                </div>

                {/* 底部装饰线 */}
                <div className="h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </article>
            ))}
          </div>

          {/* 查看更多按钮 */}
          <div className="text-center mt-8">
            <Link
              href={getLocalizedHref("/posts")}
              className="btn-gradient-border group px-8 py-3 font-semibold"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              {t('viewAllArticles')}
              <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
