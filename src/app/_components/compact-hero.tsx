"use client";

import { SITE_NAME, SITE_DESCRIPTION } from "@/lib/constants";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';

export function CompactHero() {
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isMounted, setIsMounted] = useState(false);

  const t = useTranslations('pages.home.hero');
  const tSite = useTranslations('site');
  const locale = useLocale();

  useEffect(() => {
    setIsMounted(true);
    // Delay visibility to ensure smooth animation after mount
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [isMounted]);

  return (
    <section className="relative pt-12 md:pt-16 pb-8 md:pb-10 overflow-hidden">
      {/* 顶部分隔线 */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent"></div>

      {/* 动态背景装饰 */}
      <div className="absolute inset-0 -z-10">
        {/* 渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-white to-purple-50/60 dark:from-slate-900/95 dark:via-slate-800/90 dark:to-blue-900/40"></div>

        {/* 动态光效 */}
        {isMounted && (
          <div
            className="absolute inset-0 opacity-30 transition-all duration-300"
            style={{
              background: `radial-gradient(600px circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(59, 130, 246, 0.15), transparent 40%)`,
            }}
          ></div>
        )}

        {/* 几何装饰 */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>

        {/* 网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:50px_50px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>
      </div>

      <div className="mx-auto px-4">
        {/* 内容容器 */}
        <div className="relative max-w-5xl mx-auto">
          {/* 容器背景 */}
          <div className="absolute inset-0 bg-white/40 dark:bg-slate-800/40 backdrop-blur-sm rounded-3xl border border-white/50 dark:border-slate-700/50"></div>

          <div className={`relative max-w-4xl mx-auto text-center px-6 py-8 md:px-10 md:py-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          {/* 头像 */}
          <div className="mb-6 group">
            <div className="relative w-24 h-24 md:w-28 md:h-28 mx-auto mb-4">
              {/* 模糊背景 */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500/20 via-purple-500/20 to-pink-500/20 blur-2xl scale-110"></div>

              {/* 头像容器 */}
              <div className="relative w-full h-full rounded-full bg-white dark:bg-slate-900 p-1 shadow-2xl ring-4 ring-white/20 dark:ring-slate-800/50">
                <Image
                  src="/images/avatar1.jpg"
                  alt="博主头像"
                  fill
                  className="rounded-full object-cover group-hover:scale-105 transition-transform duration-500"
                  priority
                />
              </div>

              {/* 在线状态指示器 */}
              <div className="absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-slate-900 shadow-lg">
                <div className="absolute inset-0 bg-green-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* 动态标签 */}
          <div className={`mb-6 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 backdrop-blur-sm border border-blue-200/50 dark:border-blue-700/50 rounded-full text-xs font-medium shadow-lg hover:shadow-xl transition-all duration-300 group">
              <div className="relative">
                <span className="w-2.5 h-2.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full block animate-pulse"></span>
                <span className="absolute inset-0 w-2.5 h-2.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-ping"></span>
              </div>
              <span className="bg-gradient-to-r from-blue-700 to-purple-700 dark:from-blue-300 dark:to-purple-300 bg-clip-text text-transparent font-semibold">
                {t('badge')}
              </span>
              <svg className="w-4 h-4 text-blue-500 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>

          {/* 主标题 */}
          <h1 className={`text-2xl md:text-4xl lg:text-5xl font-bold tracking-tight leading-tight mb-4 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <span className="relative inline-block">
              <span className="bg-gradient-to-r from-slate-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                {tSite('name')}
              </span>
              {/* 文字光效 */}
              <span className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 bg-clip-text text-transparent blur-lg opacity-60">
                {tSite('name')}
              </span>
            </span>
          </h1>

          {/* 副标题 */}
          <p className={`text-base md:text-lg text-slate-600 dark:text-slate-300 leading-relaxed mb-6 max-w-3xl mx-auto font-normal transition-all duration-1000 delay-700 text-center ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <span className="relative inline-block">
              {tSite('description')}
              <span className="absolute -bottom-1 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent"></span>
            </span>
          </p>

          {/* 现代化按钮组 */}
          <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-10 transition-all duration-1000 delay-900 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Link
              href="#featured"
              className="group relative px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
            >
              {/* 按钮光效 */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              <div className="absolute inset-0 bg-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12"></div>

              <div className="relative flex items-center gap-3">
                <svg className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
                <span>{t('browseArticles')}</span>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
              </div>
            </Link>

            <Link
              href={locale === defaultLocale ? '/about' : `/${locale}/about`}
              className="group relative px-5 py-3 text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 font-medium flex items-center gap-2 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-slate-800/80 transition-all duration-300 hover:shadow-lg"
            >
              <span>{t('aboutBlog')}</span>
              <svg className="w-4 h-4 group-hover:translate-x-1 group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>

          {/* 现代化统计卡片 */}
          <div className={`grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-xl mx-auto transition-all duration-1000 delay-1100 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="group relative p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl border border-slate-200/50 dark:border-slate-700/50 hover:border-blue-300/50 dark:hover:border-blue-600/50 transition-all duration-300 hover:shadow-lg hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative text-center">
                <div className="w-10 h-10 mx-auto mb-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 0l3 3m-3-3l3-3" />
                  </svg>
                </div>
                <div className="text-xl font-bold text-slate-900 dark:text-white mb-1">50+</div>
                <div className="text-xs text-slate-600 dark:text-slate-400 font-medium">{t('stats.articles')}</div>
              </div>
            </div>

            <div className="group relative p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl border border-slate-200/50 dark:border-slate-700/50 hover:border-purple-300/50 dark:hover:border-purple-600/50 transition-all duration-300 hover:shadow-lg hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative text-center">
                <div className="w-10 h-10 mx-auto mb-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <div className="text-xl font-bold text-slate-900 dark:text-white mb-1">10K+</div>
                <div className="text-xs text-slate-600 dark:text-slate-400 font-medium">{t('stats.views')}</div>
              </div>
            </div>

            <div className="group relative p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl border border-slate-200/50 dark:border-slate-700/50 hover:border-green-300/50 dark:hover:border-green-600/50 transition-all duration-300 hover:shadow-lg hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative text-center">
                <div className="w-10 h-10 mx-auto mb-2 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-xl font-bold text-slate-900 dark:text-white mb-1">
                  <span className="inline-flex items-center gap-1">
                    {t('stats.updating')}
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  </span>
                </div>
                <div className="text-xs text-slate-600 dark:text-slate-400 font-medium">{t('stats.updating')}</div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </section>
  );
}
