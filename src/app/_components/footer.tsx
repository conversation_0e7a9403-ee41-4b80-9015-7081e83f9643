"use client";

import Link from "next/link";
import Image from "next/image";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';

function Footer() {
  const currentYear = new Date().getFullYear();
  const t = useTranslations('footer');
  const tSite = useTranslations('site');
  const locale = useLocale();



  const socialLinks = [
    {
      name: "GitHub",
      href: "https://github.com/pythonsir",
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
        </svg>
      )
    },
    {
      name: "RSS",
      href: "/feed.xml",
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M6.503 20.752c0 1.794-1.456 3.248-3.251 3.248-1.796 0-3.252-1.454-3.252-3.248 0-1.794 1.456-3.248 3.252-3.248 1.795.001 3.251 1.454 3.251 3.248zm-6.503-12.572v4.811c6.05.062 10.96 4.966 11.022 11.009h4.817c-.062-8.71-7.118-15.758-15.839-15.82zm0-3.368c10.58.046 19.152 8.594 19.183 19.188h4.817c-.03-13.231-10.755-23.954-24-24v4.812z" />
        </svg>
      )
    }
  ];

  return (
    <footer className="relative bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 text-white overflow-hidden">
      {/* 动态背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute inset-0 bg-gradient-to-t from-slate-950/50 via-transparent to-transparent"></div>
      </div>

      <div className="container-custom">
        <div className="pt-8 lg:pt-8">
          {/* 主要内容区域 */}
          <div className="flex flex-col lg:flex-row items-center justify-center gap-8 mb-8">
            {/* 品牌区域 */}
            <div className="flex flex-col items-center text-center space-y-4 w-full lg:w-auto">
              <div className="flex items-center gap-4 justify-center">
                <Image
                  src="/images/avatar1.jpg"
                  alt="头像"
                  width={48}
                  height={48}
                  className="rounded-full border border-slate-600"
                />
                <div className="text-left">
                  <h3 className="text-xl font-bold">
                    <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      {tSite('name')}
                    </span>
                  </h3>
                  <p className="text-sm text-slate-400">{locale === defaultLocale ? '技术博客' : 'Tech Blog'}</p>
                </div>
              </div>

              <p className="text-slate-300 text-sm leading-relaxed max-w-md text-center">
                {t('description')}
              </p>

              {/* 邮箱和订阅区域 */}
              <div className="flex flex-col sm:flex-row items-center gap-4 justify-center">
                <div className="flex items-center gap-2 text-slate-400 text-sm">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span>{t('email')}: <EMAIL></span>
                </div>
              </div>

              {/* 社交媒体 */}
              <div className="flex gap-3 justify-center">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    {...(social.href.startsWith('http') ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                    className="w-8 h-8 bg-slate-800 hover:bg-blue-600 rounded-lg flex items-center justify-center text-slate-400 hover:text-white transition-all duration-300"
                    aria-label={social.name}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>

            {/* 微信公众号二维码 */}
            <div className="flex flex-col items-center w-full lg:w-auto">
              <h4 className="text-sm font-semibold text-slate-300 mt-1 mb-3 text-center">{t('follow')} {t('wechat')}</h4>
              <div className="bg-white p-3 rounded-lg shadow-lg mx-auto">
                <Image
                  src="/images/qrcode.jpg"
                  alt="微信公众号二维码"
                  width={100}
                  height={100}
                  className="rounded"
                />
              </div>
              <p className="text-xs text-slate-400 mt-2 text-center max-w-xs">{locale === defaultLocale ? '扫码关注获取更多技术内容' : 'Scan to follow for more tech content'}</p>
            </div>
          </div>

          {/* 分割线 */}
          <div className="h-px bg-slate-700"></div>



          {/* 底部信息 */}
          <div className="flex flex-col sm:flex-row items-center justify-center py-4 gap-2 text-sm text-slate-400">
            {/* 版权信息 */}
            <div className="flex items-center gap-2">
              <span>© {currentYear}</span>
              <span className="text-blue-400 font-medium">{tSite('name')}</span>
            </div>

            {/* 链接区域 */}
            <div className="flex items-center gap-2">
              <Link href={locale === defaultLocale ? '/privacy' : `/${locale}/privacy`} className="hover:text-blue-400 transition-colors">
                {t('privacy')}
              </Link>
              <span>•</span>
              <Link href={locale === defaultLocale ? '/terms' : `/${locale}/terms`} className="hover:text-blue-400 transition-colors">
                {t('terms')}
              </Link>
              <span>•</span>
              <Link href={locale === defaultLocale ? '/ads-policy' : `/${locale}/ads-policy`} className="hover:text-blue-400 transition-colors">
                {t('ads')}
              </Link>
              <span>•</span>
              <button
                onClick={() => {
                  if (typeof window !== 'undefined') {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }
                }}
                className="hover:text-blue-400 transition-colors"
                aria-label={locale === defaultLocale ? '回到顶部' : 'Back to top'}
              >
                {locale === defaultLocale ? '回到顶部 ↑' : 'Back to top ↑'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
