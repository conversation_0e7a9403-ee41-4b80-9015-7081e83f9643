import CoverImage from "@/app/_components/cover-image";
import { type Author } from "@/interfaces/author";
import Link from "next/link";
import LocalizedDateFormatter from "./localized-date-formatter";
import { calculateReadingTime } from "@/lib/reading-time";

type Props = {
  title: string;
  coverImage: string;
  date: string;
  excerpt: string;
  author: Author;
  slug: string;
  content: string;
  translations?: {
    readingTime: string;
    aboutMinutes: string;
    publishedOn: string;
  };
};

export function HeroPost({
  title,
  coverImage,
  date,
  excerpt,
  author,
  slug,
  content,
  translations,
}: Props) {
  return (
    <section id="posts" className="relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-full blur-3xl opacity-30"></div>
      </div>

      <div className="container-custom py-16">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-4">
            ✨ 精选文章
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            最新技术分享
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
            深入探讨软件开发的方方面面，分享实用的技术经验和最佳实践
          </p>
        </div>

        {/* 主要文章卡片 */}
        <article className="card-hover overflow-hidden max-w-6xl mx-auto animate-fade-in">
          {/* 封面图片 */}
          <div className="relative h-64 md:h-80 lg:h-96 overflow-hidden">
            <CoverImage title={title} src={coverImage} slug={slug} />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

            {/* 浮动标签 */}
            <div className="absolute top-6 left-6">
              <span className="glass px-3 py-1 text-white text-sm font-medium rounded-full">
                热门推荐
              </span>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-8 md:p-12">
            <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
              {/* 主要内容 */}
              <div className="lg:col-span-2">
                <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold leading-tight mb-6 text-neutral-900 dark:text-neutral-100">
                  <Link
                    href={`/posts/${slug}`}
                    className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300 group"
                  >
                    {title}
                    <span className="inline-block ml-2 transform group-hover:translate-x-1 transition-transform duration-300">
                      →
                    </span>
                  </Link>
                </h3>

                <p className="text-lg leading-relaxed text-neutral-600 dark:text-neutral-300 mb-6">
                  {excerpt}
                </p>

                {/* 阅读全文按钮 - 使用发光效果突出重要性 */}
                <Link
                  href={`/posts/${slug}`}
                  className="btn-glow group"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  阅读全文
                  <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>

              {/* 侧边信息 */}
              <div className="lg:col-span-1">
                <div className="bg-neutral-50 dark:bg-neutral-800/50 rounded-xl p-6 space-y-6">
                  {/* 发布日期 */}
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        {translations?.publishedOn || '发布时间'}
                      </p>
                      <p className="font-medium text-neutral-900 dark:text-neutral-100">
                        <LocalizedDateFormatter dateString={date} />
                      </p>
                    </div>
                  </div>

                  {/* 作者信息 */}
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-secondary-100 dark:bg-secondary-900/30 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-secondary-600 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">作者</p>
                      <p className="font-medium text-neutral-900 dark:text-neutral-100">
                        {author.name}
                      </p>
                    </div>
                  </div>

                  {/* 阅读时间估算 */}
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        {translations?.readingTime || '阅读时间'}
                      </p>
                      <p className="font-medium text-neutral-900 dark:text-neutral-100">
                        {translations?.aboutMinutes?.replace('{minutes}', calculateReadingTime(content).toString()) || `约 ${calculateReadingTime(content)} 分钟`}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </article>
      </div>
    </section>
  );
}
