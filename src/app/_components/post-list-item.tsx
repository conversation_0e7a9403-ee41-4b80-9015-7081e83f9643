"use client";

import Link from "next/link";
import Image from "next/image";
import { type Author } from "@/interfaces/author";
import LocalizedDateFormatter from "./localized-date-formatter";
import { ViewCounter } from "./view-counter";
import { ReadingTime } from "./reading-time";
import { useTranslations } from 'next-intl';

type Props = {
  title: string;
  coverImage: string;
  date: string;
  excerpt: string;
  author: Author;
  slug: string;
  featured?: boolean;
  content: string;
};

export function PostListItem({
  title,
  coverImage,
  date,
  excerpt,
  author,
  slug,
  featured = false,
  content,
}: Props) {
  const t = useTranslations('components.postListItem');

  return (
    <div className="h-full flex flex-col">
      {/* 封面图片 */}
      <div className="relative h-40 sm:h-36 md:h-60 lg:h-60 overflow-hidden">
        <Image
          src={coverImage}
          alt={title}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-500"
        />

        {/* 渐变遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {featured && (
          <div className="absolute top-2 left-2 z-10">
            <span className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-md">
              {t('featuredBadge')}
            </span>
          </div>
        )}

        {/* 阅读时间标签 */}
        <div className="absolute top-2 right-2 z-10">
          <ReadingTime
            content={content}
            format="full"
            className="bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs"
          />
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-3 md:p-4 flex-1 flex flex-col">
        {/* 文章元信息 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
            <LocalizedDateFormatter dateString={date} />
            <span className="text-slate-400">•</span>
            <ViewCounter slug={slug} size="sm" showIcon={false} compact={true} />
          </div>
          <ReadingTime
            content={content}
            format="full"
            className="text-xs text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded-full"
          />
        </div>

        {/* 标题 */}
        <h3 className="text-sm md:text-base lg:text-lg font-bold leading-tight mb-2 text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
          <Link href={`/posts/${slug}`} className="line-clamp-2">
            {title}
          </Link>
        </h3>

        {/* 摘要 */}
        <p className="text-slate-600 dark:text-slate-300 leading-relaxed mb-4 line-clamp-2 text-xs md:text-sm flex-1">
          {excerpt}
        </p>

        {/* 作者信息 */}
        <div className="flex items-center gap-2 mt-auto">
          <div className="relative w-6 h-6">
            <Image
              src="/images/avatar1.jpg"
              alt={author.name}
              fill
              className="rounded-full object-cover ring-1 ring-slate-200 dark:ring-slate-700"
            />
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-xs font-medium text-slate-900 dark:text-white truncate">
              {author.name}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
