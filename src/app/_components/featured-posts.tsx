import Link from "next/link";
import Image from "next/image";
import { type Author } from "@/interfaces/author";
import CoverImage from "./cover-image";
import DateFormatter from "./date-formatter";

type Post = {
  slug: string;
  title: string;
  date: string;
  coverImage: string;
  author: Author;
  excerpt: string;
};

type Props = {
  posts: Post[];
};

export function FeaturedPosts({ posts }: Props) {
  return (
    <section id="featured" className="py-24 md:py-32 relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-pink-200/30 to-purple-200/30 dark:from-pink-800/20 dark:to-purple-800/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-pink-100 to-purple-100 dark:from-pink-900/30 dark:to-purple-900/30 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium mb-4">
              ⭐ 精选文章
            </span>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white mb-6">
              热门<span className="text-gradient-primary">技术</span>分享
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed">
              精心挑选的技术文章，涵盖前端开发、后端架构、最佳实践等多个领域
            </p>
          </div>

          {/* 文章网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6 lg:gap-8">
            {posts.map((post, index) => (
              <article
                key={post.slug}
                className="group relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border border-slate-200/50 dark:border-slate-700/50 hover:border-blue-300/50 dark:hover:border-blue-600/50"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                {/* 封面图片 */}
                <div className="relative h-40 sm:h-44 md:h-40 lg:h-44 xl:h-40 overflow-hidden">
                  <CoverImage
                    slug={post.slug}
                    title={post.title}
                    src={post.coverImage}
                  />
                  
                  {/* 渐变遮罩 */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  {/* 特色标签 */}
                  {index === 0 && (
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-sm font-medium rounded-full shadow-lg">
                        🔥 热门推荐
                      </span>
                    </div>
                  )}
                  
                  {/* 悬浮阅读按钮 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                    <Link
                      href={`/posts/${post.slug}`}
                      className="btn-3d"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      阅读文章
                    </Link>
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="p-4 lg:p-5 xl:p-6">
                  {/* 日期 */}
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">
                      <DateFormatter dateString={post.date} />
                    </span>
                  </div>

                  {/* 标题 */}
                  <h3 className="text-base md:text-lg lg:text-xl font-bold leading-tight mb-4 text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    <Link href={`/posts/${post.slug}`}>
                      {post.title}
                    </Link>
                  </h3>

                  {/* 摘要 */}
                  <p className="text-xs md:text-sm text-slate-600 dark:text-slate-300 leading-relaxed mb-4 line-clamp-2 lg:line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* 底部信息 */}
                  <div className="flex items-center justify-between">
                    {/* 作者信息 */}
                    <div className="flex items-center gap-3">
                      <div className="relative w-8 h-8">
                        <Image
                          src="/images/avatar1.jpg"
                          alt={post.author.name}
                          fill
                          className="rounded-full object-cover"
                        />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-slate-900 dark:text-white">
                          {post.author.name}
                        </p>
                      </div>
                    </div>

                    {/* 阅读更多 */}
                    <Link
                      href={`/posts/${post.slug}`}
                      className="btn-gradient-border text-sm group/link"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 0l3 3m-3-3l3-3" />
                      </svg>
                      阅读更多
                      <svg className="w-4 h-4 transform group-hover/link:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </Link>
                  </div>
                </div>

                {/* 底部装饰线 */}
                <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </article>
            ))}
          </div>

          {/* 查看更多按钮 */}
          <div className="text-center mt-16">
            <Link
              href="#latest"
              className="btn-magnetic group px-8 py-4 text-lg font-semibold"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
              查看更多文章
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
