"use client";

import { parseISO, format } from "date-fns";
import { zhCN, enUS } from "date-fns/locale";
import { useTranslations, useLocale } from 'next-intl';

type Props = {
  dateString: string;
  formatType?: 'full' | 'short' | 'minimal';
};

const LocalizedDateFormatter = ({ dateString, formatType = 'full' }: Props) => {
  const t = useTranslations('common');
  const locale = useLocale();

  const date = parseISO(dateString);

  // 根据语言选择 date-fns locale
  const dateFnsLocale = locale === 'zh' ? zhCN : enUS;

  // 获取对应的日期格式
  const dateFormat = t('dateFormat');

  return (
    <time dateTime={dateString} suppressHydrationWarning>
      {format(date, dateFormat, { locale: dateFnsLocale })}
    </time>
  );
};

export default LocalizedDateFormatter;
