import Avatar from "./avatar";
import LocalizedDateFormatter from "./localized-date-formatter";
import { PostTitle } from "@/app/_components/post-title";
import { type Author } from "@/interfaces/author";
import { ViewCounter } from "./view-counter";

type Props = {
  title: string;
  date: string;
  author: Author;
  slug?: string;
};

export function PostHeader({ title, date, author, slug }: Props) {
  return (
    <div className="mb-6 sm:mb-8">
      <PostTitle>{title}</PostTitle>

      {/* 作者和日期信息 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 pb-6 sm:pb-8 border-b border-slate-200 dark:border-slate-700">
        <Avatar name={author.name} picture={author.picture} />
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm sm:text-base text-slate-600 dark:text-slate-400">
          <div className="flex items-center gap-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <LocalizedDateFormatter dateString={date} />
          </div>
          {slug && (
            <>
              <span className="hidden sm:inline text-slate-400">•</span>
              <ViewCounter slug={slug} size="md" className="inherit-text-size" />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
