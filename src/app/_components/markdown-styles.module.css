.markdown {
  font-size: 0.95rem;
  line-height: 1.7;
  color: #334155;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

@media (min-width: 640px) {
  .markdown {
    font-size: 1.125rem;
    line-height: 1.8;
  }
}

.markdown p,
.markdown ul,
.markdown ol,
.markdown blockquote {
  margin: 1rem 0;
}

@media (min-width: 640px) {
  .markdown p,
  .markdown ul,
  .markdown ol,
  .markdown blockquote {
    margin: 1.5rem 0;
  }
}

.markdown h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  color: #0f172a;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

@media (min-width: 640px) {
  .markdown h1 {
    font-size: 2.25rem;
    margin: 4rem 0 1.5rem 0;
  }
}

.markdown h2 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #0f172a;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.25rem;
}

@media (min-width: 640px) {
  .markdown h2 {
    font-size: 1.875rem;
    margin: 3rem 0 1.5rem 0;
  }
}

.markdown h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  color: #0f172a;
}

@media (min-width: 640px) {
  .markdown h3 {
    font-size: 1.5rem;
    margin: 2.5rem 0 1rem 0;
  }
}

.markdown h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  color: #0f172a;
}

@media (min-width: 640px) {
  .markdown h4 {
    font-size: 1.25rem;
    margin: 2rem 0 0.75rem 0;
  }
}

.markdown code {
  background-color: #f1f5f9;
  color: #1e293b;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: "JetBrains Mono", "Fira Code", "Consolas", monospace;
  word-break: break-word;
  overflow-wrap: break-word;
}

@media (max-width: 640px) {
  .markdown code {
    font-size: 0.75rem;
    padding: 0.125rem 0.25rem;
  }
}

.markdown pre {
  background-color: #0f172a;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  width: 100%;
}

@media (min-width: 640px) {
  .markdown pre {
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin: 2rem 0;
  }
}

.markdown pre code {
  background: transparent;
  color: #f1f5f9;
  padding: 0;
  font-size: 0.75rem;
  font-family: "JetBrains Mono", "Fira Code", "Consolas", monospace;
  line-height: 1.5;
}

@media (min-width: 640px) {
  .markdown pre code {
    font-size: 0.875rem;
    line-height: 1.6;
  }
}

.markdown ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.markdown ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
}

.markdown li {
  margin: 0.5rem 0;
  color: #334155;
}

.markdown blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  background-color: #eff6ff;
  border-radius: 0 0.5rem 0.5rem 0;
  margin-left: 0;
  margin-right: 0;
  overflow-wrap: break-word;
}

@media (min-width: 640px) {
  .markdown blockquote {
    padding-left: 1.5rem;
  }
}

.markdown a {
  color: #2563eb;
  text-decoration: underline;
  text-underline-offset: 2px;
  word-break: break-all;
  overflow-wrap: break-word;
}

.markdown a:hover {
  color: #1d4ed8;
}

.markdown img {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 1.5rem 0;
  max-width: 100%;
  height: auto;
}

@media (min-width: 640px) {
  .markdown img {
    margin: 2rem 0;
  }
}

.markdown table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  display: block;
  overflow-x: auto;
  white-space: nowrap;
}

@media (max-width: 640px) {
  .markdown table {
    font-size: 0.75rem;
    margin: 1rem 0;
  }
}

.markdown th,
.markdown td {
  border: 1px solid #d1d5db;
  padding: 0.5rem 0.75rem;
  text-align: left;
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .markdown th,
  .markdown td {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
}

.markdown th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #111827;
}

.markdown strong {
  font-weight: 600;
  color: #0f172a;
}

.markdown em {
  font-style: italic;
  color: #475569;
}

/* Mermaid图表样式 */
.markdown .mermaid-container {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #fefefe;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  overflow-x: auto;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.markdown .mermaid-container svg {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* 响应式Mermaid图表 */
@media (max-width: 640px) {
  .markdown .mermaid-container {
    margin: 1.5rem -1rem;
    padding: 1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}

/* 隐藏已处理的mermaid代码块 */
.markdown pre.mermaid-processed {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}
