import Image from "next/image";

export function QuickAbout() {
  return (
    <section id="about" className="py-16 md:py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* 主要内容卡片 */}
          <div className="bg-gradient-to-br from-slate-900 to-blue-900 dark:from-slate-800 dark:to-blue-800 rounded-2xl p-8 md:p-12 text-white relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 -z-10">
              <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400/20 rounded-full blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-purple-400/20 rounded-full blur-2xl"></div>
            </div>

            <div className="grid md:grid-cols-3 gap-8 items-center">
              {/* 左侧内容 */}
              <div className="md:col-span-2">
                <h2 className="text-2xl md:text-3xl font-bold mb-4">
                  关于这个博客
                </h2>
                <p className="text-blue-100 leading-relaxed mb-6">
                  专注于分享实用的软件开发经验、深度的技术洞察和前沿的开发趋势。
                  每篇文章都经过精心编写，力求为读者提供有价值的技术内容。
                </p>
                
                {/* 特色列表 */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <span className="text-blue-100">深度技术解析</span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <span className="text-blue-100">实战项目经验</span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <span className="text-blue-100">持续学习分享</span>
                  </div>
                </div>
              </div>

              {/* 微信公众号二维码 */}
              <div className="flex flex-col items-center">
                <div className="bg-white rounded-2xl p-4 shadow-lg mb-4">
                  <Image
                    src="/images/qrcode.jpg"
                    alt="微信公众号二维码"
                    width={160}
                    height={160}
                    className="rounded-lg"
                  />
                </div>
                <div className="text-center">
                  <h4 className="text-lg font-semibold text-white mb-2">关注公众号</h4>
                  <p className="text-blue-200 text-sm">获取最新技术文章推送</p>
                </div>
              </div>
            </div>

            {/* 统计数据移到底部 */}
            <div className="mt-8 pt-8 border-t border-white/10">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-300 mb-2">50+</div>
                  <div className="text-sm text-blue-200">技术文章</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-300 mb-2">10K+</div>
                  <div className="text-sm text-blue-200">总阅读量</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-300 mb-2">5+</div>
                  <div className="text-sm text-blue-200">技术领域</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">2+</div>
                  <div className="text-sm text-blue-200">年经验</div>
                </div>
              </div>
            </div>

            {/* 技术标签 */}
            <div className="mt-8 pt-8 border-t border-white/10">
              <div className="flex flex-wrap gap-2 justify-center">
                {['React', 'Next.js', 'TypeScript', 'Node.js', 'Python', 'AI/ML'].map((tech) => (
                  <span
                    key={tech}
                    className="px-3 py-1 bg-white/10 backdrop-blur-sm text-white text-sm rounded-full border border-white/20"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>

            {/* 联系方式 */}
            <div className="mt-8 text-center">
              <p className="text-blue-200 text-sm mb-4">
                有问题或建议？欢迎交流讨论
              </p>
              <div className="flex justify-center gap-4">
                <a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white text-sm rounded-lg border border-white/20 transition-colors duration-300"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                  GitHub
                </a>
                
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white text-sm rounded-lg border border-white/20 transition-colors duration-300"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  邮箱
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
