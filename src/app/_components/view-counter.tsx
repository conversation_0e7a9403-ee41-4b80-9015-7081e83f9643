"use client";

import { useViewCount, formatViewCount } from '@/hooks/useViewCount';
import { useTranslations } from 'next-intl';

interface Props {
  slug: string;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  compact?: boolean;
}

export function ViewCounter({ slug, className = '', showIcon = true, size = 'md', compact = false }: Props) {
  const { views, loading, error } = useViewCount(slug);
  const t = useTranslations('components.viewCounter');

  // 根据尺寸设置样式
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  // 加载中或出错时显示占位符，避免hydration mismatch
  if (error || loading) {
    return (
      <div className={`flex items-center gap-1.5 text-slate-400 dark:text-slate-500 ${sizeClasses[size]} ${className}`}>
        {showIcon && (
          <svg
            className={`${iconSizes[size]} flex-shrink-0 animate-pulse`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
            />
          </svg>
        )}
        <span className="font-medium animate-pulse">
          {compact ? t('loadingCompact') : t('loadingFull')}
        </span>
      </div>
    );
  }

  // 如果 className 包含 inherit-text-size，则不应用默认的文字大小
  const shouldInheritTextSize = className.includes('inherit-text-size');
  const textSizeClass = shouldInheritTextSize ? '' : sizeClasses[size];

  return (
    <div className={`flex items-center gap-1.5 text-slate-600 dark:text-slate-400 ${textSizeClass} ${className}`}>
      {showIcon && (
        <svg
          className={`${iconSizes[size]} flex-shrink-0`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
          />
        </svg>
      )}

      <span className="font-medium">
        {compact ? `${formatViewCount(views)} ${t('viewsCompact')}` : `${formatViewCount(views)} ${t('viewsFull')}`}
      </span>
    </div>
  );
}

// 简化版本，只显示数字
export function ViewCountBadge({ slug, className = '' }: { slug: string; className?: string }) {
  const { views, loading, error } = useViewCount(slug);
  // 这个组件不需要翻译，因为它只显示数字

  if (error || loading) {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 dark:bg-slate-700 text-slate-400 dark:text-slate-500 ${className}`}>
        <svg className="w-3 h-3 mr-1 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        <span className="animate-pulse">--</span>
      </span>
    );
  }

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-slate-200 ${className}`}>
      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>
      {formatViewCount(views)}
    </span>
  );
}

// 大型显示版本，用于文章详情页
export function ViewCountDisplay({ slug, className = '' }: { slug: string; className?: string }) {
  const { views, loading, error } = useViewCount(slug);
  const t = useTranslations('components.viewCounter');

  // 加载中或出错时显示占位符
  if (error || loading) {
    return (
      <div className={`flex items-center gap-3 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 ${className}`}>
        <div className="flex items-center justify-center w-12 h-12 bg-slate-100 dark:bg-slate-700 rounded-full">
          <svg className="w-6 h-6 text-slate-400 dark:text-slate-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        </div>
        <div className="flex-1">
          <div className="text-sm text-slate-600 dark:text-slate-400 mb-1">
            {t('articleViews')}
          </div>
          <div className="text-2xl font-bold text-slate-400 dark:text-slate-500">
            <span className="animate-pulse">{t('loadingFull')}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 ${className}`}>
      <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full">
        <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      </div>

      <div className="flex-1">
        <div className="text-sm text-slate-600 dark:text-slate-400 mb-1">
          {t('articleViews')}
        </div>
        <div className="text-2xl font-bold text-slate-900 dark:text-white">
          <span>{formatViewCount(views)} {t('viewsCount')}</span>
        </div>
      </div>
    </div>
  );
}
