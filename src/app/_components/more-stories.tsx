import { Post } from "@/interfaces/post";
import { PostPreview } from "./post-preview";

type Props = {
  posts: Post[];
};

export function MoreStories({ posts }: Props) {
  return (
    <section className="relative py-16">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 left-1/3 w-72 h-72 bg-gradient-to-br from-secondary-100 to-primary-100 dark:from-secondary-900/20 dark:to-primary-900/20 rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="container-custom">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-secondary-100 dark:bg-secondary-900/30 text-secondary-700 dark:text-secondary-300 rounded-full text-sm font-medium mb-4">
            📚 更多精彩
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            技术文章
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
            探索更多关于软件开发、编程技巧和技术趋势的深度文章
          </p>
        </div>

        {/* 文章网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 lg:gap-12">
          {posts.map((post, index) => (
            <div
              key={post.slug}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <PostPreview
                title={post.title}
                coverImage={post.coverImage}
                date={post.date}
                author={post.author}
                slug={post.slug}
                excerpt={post.excerpt}
              />
            </div>
          ))}
        </div>

        {/* 底部装饰 */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-2 text-neutral-500 dark:text-neutral-400">
            <div className="w-8 h-px bg-neutral-300 dark:bg-neutral-600"></div>
            <span className="text-sm">持续更新中</span>
            <div className="w-8 h-px bg-neutral-300 dark:bg-neutral-600"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
