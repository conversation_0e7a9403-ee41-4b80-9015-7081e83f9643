"use client";

import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { locales, defaultLocale } from '@/i18n';

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    // Set initial scroll state
    handleScroll();
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Prevent hydration mismatch by not applying scroll-based styles until mounted
  const navClasses = isMounted && isScrolled
    ? "bg-white dark:bg-slate-900 backdrop-blur-xl shadow-xl border-b border-gray-200/50 dark:border-slate-700/50"
    : "bg-white dark:bg-slate-900 backdrop-blur-lg shadow-lg border-b border-gray-200/30 dark:border-slate-800/30";

  const t = useTranslations('nav');
  const tSite = useTranslations('site');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  const navItems = [
    { href: getLocalizedHref('/'), label: t('home') },
    { href: getLocalizedHref('/posts'), label: t('posts') },
    { href: getLocalizedHref('/tools'), label: t('tools') },
    { href: getLocalizedHref('/about'), label: t('about') },
  ];

  const handleLanguageChange = (newLocale: string) => {
    const segments = pathname.split('/').filter(Boolean); // 移除空字符串

    // 检查当前路径是否已经有语言前缀
    const hasLanguagePrefix = segments[0] && locales.includes(segments[0] as any);

    // 获取不包含语言前缀的路径部分
    const pathWithoutLocale = hasLanguagePrefix ? segments.slice(1) : segments;

    // 如果切换到默认语言，不添加语言前缀
    if (newLocale === defaultLocale) {
      const newPath = pathWithoutLocale.length > 0 ? `/${pathWithoutLocale.join('/')}` : '/';
      router.push(newPath);
    } else {
      // 非默认语言，添加语言前缀
      const newPath = pathWithoutLocale.length > 0 ? `/${newLocale}/${pathWithoutLocale.join('/')}` : `/${newLocale}`;
      router.push(newPath);
    }
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${navClasses}`}
    >
      <div className="container-custom">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <Link href={getLocalizedHref('/')} className="flex items-center gap-3 group">
            <div className="relative w-10 h-10 group-hover:scale-110 transition-transform duration-300">
              <Image
                src="/images/avatar1.jpg"
                alt="博主头像"
                fill
                className="rounded-lg object-cover ring-2 ring-blue-500/20 group-hover:ring-blue-500/40 transition-all duration-300"
              />
            </div>
            <span className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
              {tSite('name')}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            {/* Language Switcher */}
            <div className="relative group">
              <button className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors">
                <div className="w-5 h-4 rounded-sm overflow-hidden flex items-center justify-center border border-gray-300 dark:border-gray-600">
                  {locale === defaultLocale ? (
                    <div className="w-full h-full bg-red-600 flex items-center justify-center">
                      <span className="text-yellow-400 text-xs font-bold">CN</span>
                    </div>
                  ) : (
                    <div className="w-full h-full bg-blue-700 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">US</span>
                    </div>
                  )}
                </div>
                <span>{locale === defaultLocale ? '中文' : 'EN'}</span>
                <svg className="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute top-full right-0 mt-2 w-36 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                {locales.map((loc) => (
                  <button
                    key={loc}
                    onClick={() => handleLanguageChange(loc)}
                    className={`w-full flex items-center gap-2 px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                      locale === loc ? 'text-blue-600 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    <div className="w-5 h-4 rounded-sm overflow-hidden flex items-center justify-center border border-gray-300 dark:border-gray-600">
                      {loc === defaultLocale ? (
                        <div className="w-full h-full bg-red-600 flex items-center justify-center">
                          <span className="text-yellow-400 text-xs font-bold">CN</span>
                        </div>
                      ) : (
                        <div className="w-full h-full bg-blue-700 flex items-center justify-center">
                          <span className="text-white text-xs font-bold">US</span>
                        </div>
                      )}
                    </div>
                    <span>{loc === defaultLocale ? '中文' : 'English'}</span>
                  </button>
                ))}
              </div>
            </div>
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-300 relative group"
              >
                {item.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-500 group-hover:w-full transition-all duration-300"></span>
              </Link>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden w-10 h-10 flex items-center justify-center text-gray-600 hover:text-blue-600 transition-colors"
          >
            <svg
              className={`w-6 h-6 transform transition-transform duration-300 ${
                isMobileMenuOpen ? "rotate-45" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        <div
          className={`md:hidden overflow-hidden transition-all duration-300 ${
            isMobileMenuOpen ? "max-h-64 opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <div className="py-4 space-y-2 border-t border-gray-200">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => setIsMobileMenuOpen(false)}
                className="block px-4 py-3 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg font-medium transition-all duration-300"
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
}
