import { parseISO, format } from "date-fns";
import { zhCN } from "date-fns/locale";

type Props = {
  dateString: string;
};

const DateFormatter = ({ dateString }: Props) => {
  const date = parseISO(dateString);
  // 使用固定的本地化设置确保服务端和客户端一致
  return (
    <time dateTime={dateString} suppressHydrationWarning>
      {format(date, "yyyy年MM月dd日", { locale: zhCN })}
    </time>
  );
};

export default DateFormatter;
