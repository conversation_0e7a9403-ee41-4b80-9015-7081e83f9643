"use client";

import { useState } from "react";

export function NewsletterSection() {
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setIsSubscribed(true);
        setEmail("");

        // 3秒后重置状态
        setTimeout(() => {
          setIsSubscribed(false);
        }, 3000);
      } else {
        alert(data.message || '订阅失败，请稍后重试');
      }
    } catch (error) {
      console.error('订阅错误:', error);
      alert('订阅失败，请稍后重试');
    }
  };

  return (
    <section className="py-24 md:py-32 relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-200/30 to-pink-200/30 dark:from-purple-800/20 dark:to-pink-800/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* 主要内容卡片 */}
          <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 dark:from-slate-800 dark:via-blue-800 dark:to-slate-800 rounded-3xl p-8 md:p-12 lg:p-16 text-white relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 -z-10">
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-br from-cyan-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
            </div>

            <div className="text-center">
              {/* 图标 */}
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-400 rounded-2xl flex items-center justify-center mx-auto mb-8">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>

              {/* 标题 */}
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                订阅技术周刊
              </h2>

              {/* 描述 */}
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
                每周精选最新的技术文章、开发工具和行业动态，直接发送到你的邮箱
              </p>

              {/* 特色列表 */}
              <div className="grid md:grid-cols-3 gap-6 mb-12">
                <div className="flex items-center gap-3 justify-center md:justify-start">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-blue-100">每周精选内容</span>
                </div>
                
                <div className="flex items-center gap-3 justify-center md:justify-start">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <span className="text-blue-100">前沿技术趋势</span>
                </div>
                
                <div className="flex items-center gap-3 justify-center md:justify-start">
                  <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <span className="text-blue-100">实用开发技巧</span>
                </div>
              </div>

              {/* 订阅表单 */}
              {!isSubscribed ? (
                <form onSubmit={handleSubmit} className="max-w-md mx-auto">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="输入你的邮箱地址"
                      required
                      className="flex-1 px-6 py-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300"
                    />
                    <button
                      type="submit"
                      className="btn-glow px-8 py-4 font-semibold whitespace-nowrap"
                    >
                      立即订阅
                    </button>
                  </div>
                  
                  <p className="text-sm text-blue-200 mt-4">
                    免费订阅，随时可以取消。订阅即表示您同意我们的
                    <a href="/privacy" className="text-blue-300 hover:text-blue-100 underline ml-1">隐私政策</a>。
                  </p>
                </form>
              ) : (
                <div className="max-w-md mx-auto">
                  <div className="bg-green-500/20 border border-green-400/30 rounded-xl p-6">
                    <div className="flex items-center justify-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <span className="text-green-300 font-semibold">订阅成功！</span>
                    </div>
                    <p className="text-green-200 text-sm">
                      感谢订阅！你将很快收到我们的技术周刊。
                    </p>
                  </div>
                </div>
              )}

              {/* 统计信息 */}
              <div className="mt-12 pt-8 border-t border-white/10">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-300 mb-1">1000+</div>
                    <div className="text-sm text-blue-200">订阅者</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-300 mb-1">50+</div>
                    <div className="text-sm text-blue-200">期刊</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-cyan-300 mb-1">95%</div>
                    <div className="text-sm text-blue-200">满意度</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-300 mb-1">每周</div>
                    <div className="text-sm text-blue-200">更新</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
