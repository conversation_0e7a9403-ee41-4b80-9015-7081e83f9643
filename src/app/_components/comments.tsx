"use client";

import Giscus from "@giscus/react";
import { useTheme } from "next-themes";
import { giscusConfig, ENABLE_COMMENTS } from "@/lib/giscus-config";

type Props = {
  title: string;
};

export function Comments({ title }: Props) {
  const { theme } = useTheme();

  // 如果评论系统被禁用，则不显示评论区域
  if (!ENABLE_COMMENTS) {
    return null;
  }

  return (
    <div className="mt-16 pt-8 border-t border-slate-200 dark:border-slate-700">
      <div className="mb-8">
        <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
          💬 评论讨论
        </h3>
        <p className="text-slate-600 dark:text-slate-400">
          欢迎对《{title}》发表评论，分享你的想法和经验
        </p>
      </div>

      <div className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-200/50 dark:border-slate-700/50">
        <Giscus
          id="comments"
          repo={giscusConfig.repo}
          repoId={giscusConfig.repoId}
          category={giscusConfig.category}
          categoryId={giscusConfig.categoryId}
          mapping={giscusConfig.mapping}
          strict={giscusConfig.strict}
          reactionsEnabled={giscusConfig.reactionsEnabled}
          emitMetadata={giscusConfig.emitMetadata}
          inputPosition={giscusConfig.inputPosition}
          theme={theme === "dark" ? "dark" : "light"}
          lang={giscusConfig.lang}
          loading={giscusConfig.loading}
        />
      </div>
    </div>
  );
}
