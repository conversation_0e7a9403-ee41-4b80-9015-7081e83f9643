import { HOME_OG_IMAGE_URL } from "@/lib/constants";
import type { Metadata } from "next";
import { getMessages, getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales } from '@/i18n';
import { NextIntlClientProvider } from 'next-intl';
import { PageTrackingProvider } from "../_components/page-tracking-provider";
import { Navbar } from "../_components/navbar";
import Footer from "../_components/footer";

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'site' });

  return {
    title: `${t('name')} - ${t('description')}`,
    description: t('description'),
    openGraph: {
      images: [HOME_OG_IMAGE_URL],
      title: `${t('name')} - ${t('description')}`,
      description: t('description'),
      type: "website" as const,
    },
    keywords: t('keywords'),
    authors: [{ name: t('author') }],
  };
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;
  // 验证语言参数
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // 获取翻译消息
  const messages = await getMessages({ locale });

  return (
    <NextIntlClientProvider messages={messages} locale={locale}>
      <PageTrackingProvider>
        <Navbar />
        <div className="min-h-screen pt-16 md:pt-20">{children}</div>
        <Footer />
      </PageTrackingProvider>
    </NextIntlClientProvider>
  );
}