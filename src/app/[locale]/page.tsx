import { <PERSON>adata } from "next";
import { CompactHero } from "../_components/compact-hero";
import { FeaturedArticle } from "../_components/featured-article";
import { ArticleGrid } from "../_components/article-grid";
import { FloatingWechat } from "../_components/floating-wechat";
import { StructuredData } from "../_components/structured-data";

import { getPrimaryFeaturedPost, getAllPosts } from "@/lib/api";
import { SITE_URL, HOME_OG_IMAGE_URL } from "@/lib/constants";
import { getTranslations } from 'next-intl/server';
import { defaultLocale } from '@/i18n';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'site' });
  const tPages = await getTranslations({ locale, namespace: 'pages.home' });
  
  return {
    title: tPages('title'),
    description: t('description'),
    keywords: t('keywords'),
    authors: [{ name: t('author'), url: SITE_URL }],
    creator: t('name'),
    publisher: t('name'),
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: `/${locale === defaultLocale ? '' : locale}`,
      languages: {
        'zh': '/',
        'en': '/en',
      },
    },
    openGraph: {
      type: "website",
      locale: locale === defaultLocale ? "zh_CN" : "en_US",
      url: SITE_URL,
      title: tPages('title'),
      description: t('description'),
      siteName: t('name'),
      images: [
        {
          url: HOME_OG_IMAGE_URL,
          width: 1200,
          height: 630,
          alt: `${t('name')} - ${tPages('title')}`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: tPages('title'),
      description: t('description'),
      images: [HOME_OG_IMAGE_URL],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function Index({ params }: Props) {
  const { locale } = await params;
  const featuredPost = getPrimaryFeaturedPost(locale);
  // 获取真正的最新文章（按日期倒序排列）
  const allPosts = getAllPosts(locale);
  const latestPosts = featuredPost
    ? allPosts.filter(post => post.slug !== featuredPost.slug).slice(0, 12)
    : allPosts.slice(0, 12);

  // 获取翻译
  const tHome = await getTranslations({ locale, namespace: 'pages.home.articles' });
  const tFeatured = await getTranslations({ locale, namespace: 'pages.home.featured' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  return (
    <main className="relative">
      {/* 简洁背景 */}
      <div className="fixed inset-0 -z-20 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"></div>

      {/* 微妙装饰 */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute top-20 right-1/4 w-64 h-64 bg-blue-100/30 dark:bg-blue-900/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-48 h-48 bg-purple-100/20 dark:bg-purple-900/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative">
        {/* 紧凑型Hero区域 */}
        <CompactHero />

        {/* 特色文章 - 大卡片展示 */}
        {featuredPost && (
          <FeaturedArticle
            post={featuredPost}
            translations={{
              badge: tFeatured('badge'),
              title: tFeatured('title'),
              imageBadge: tFeatured('imageBadge'),
              readFull: tFeatured('readFull'),
              author: tFeatured('author'),
              authortag: tFeatured('authortag')
            }}
            locale={locale}
          />
        )}

        {/* 文章网格 - 主要内容区域 */}
        <ArticleGrid
          posts={latestPosts}
          title={tHome('title')}
          description={tHome('description')}
        />
      </div>

      {/* 悬浮微信公众号 */}
      <FloatingWechat />

      {/* 结构化数据 */}
      <StructuredData
        type="website"
        title={tSite('name')}
        description={tSite('description')}
        url={SITE_URL}
      />


    </main>
  );
}