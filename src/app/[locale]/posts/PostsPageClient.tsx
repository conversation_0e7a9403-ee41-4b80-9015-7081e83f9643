'use client';

import { Post } from "@/interfaces/post";
import Header from "@/app/_components/header";
import { SearchAndFilterClient } from "@/app/_components/search-and-filter-client";

type TranslationsType = {
  title: string;
  description: string;
  subtitle: string;
};

type SearchTranslationsType = {
  searchPlaceholder: string;
  sortByDate: string;
  sortByTitle: string;
  filterAll: string;
  filterFeatured: string;
  resultsFoundPrefix: string;
  resultsFoundSuffix: string;
  resultsWithKeywordPrefix: string;
  resultsWithKeywordSuffix: string;
  noResultsTitle: string;
  noResultsDescription: string;
  resetFilters: string;
  browseAllPosts: string;
};

type PaginationTranslationsType = {
  previous: string;
  next: string;
  showingItemsPrefix: string;
  showingItemsMiddle1: string;
  showingItemsMiddle2: string;
  showingItemsSuffix: string;
  pageInfoPrefix: string;
  pageInfoMiddle: string;
  pageInfoSuffix: string;
};

type Props = {
  locale: string;
  translations: TranslationsType;
  posts: Post[];
  searchTranslations: SearchTranslationsType;
  paginationTranslations: PaginationTranslationsType;
};

export default function PostsPageClient({ locale, translations: t, posts, searchTranslations, paginationTranslations }: Props) {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* 背景装饰 */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute top-20 right-1/4 w-64 h-64 bg-blue-100/20 dark:bg-blue-900/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-48 h-48 bg-purple-100/20 dark:bg-purple-900/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container-custom">
        <Header />

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
            {t.title}
          </h1>
          <p className="text-base text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            {t.subtitle}
          </p>
        </div>

        {/* 搜索和筛选 */}
        <SearchAndFilterClient
          posts={posts}
          locale={locale}
          translations={searchTranslations}
          paginationTranslations={paginationTranslations}
        />
      </div>
    </main>
  );
}
