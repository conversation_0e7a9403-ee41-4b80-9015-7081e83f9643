"use client";
import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_URL } from "@/lib/constants";

interface IPInfo {
  ip: string;
  country: string;
  region: string;
  city: string;
  isp: string;
  org: string;
  timezone: string;
  lat: number;
  lon: number;
  as: string;
}

interface IPAPIResponse {
  status?: string;
  message?: string;
  query: string;
  country: string;
  regionName: string;
  city: string;
  isp: string;
  org: string;
  timezone: string;
  lat?: number;
  lon?: number;
  as?: string;
}

interface IPApiCoResponse {
  error?: boolean;
  reason?: string;
  ip: string;
  country_name?: string;
  country?: string;
  region?: string;
  region_code?: string;
  city: string;
  org?: string;
  asn?: string;
  timezone: string;
  latitude?: number;
  longitude?: number;
}

interface IPInfoResponse {
  error?: { message?: string };
  ip: string;
  country: string;
  region: string;
  city: string;
  org?: string;
  timezone?: string;
  loc?: string;
}

interface IPifyResponse {
  ip: string;
}

interface VoreAPIResponse {
  ipinfo?: { text?: string };
  ipdata?: {
    info1?: string;
    info2?: string;
    isp?: string;
  };
}

interface PConlineResponse {
  ip: string;
  pro: string;
  city: string;
  addr: string;
}

export default function IPLookupPage() {
  const [ipAddress, setIpAddress] = useState("");
  const [ipInfo, setIpInfo] = useState<IPInfo | null>(null);
  const [localExitIP, setLocalExitIP] = useState<string>("");
  const [webrtcIP, setWebrtcIP] = useState<string>("");

  const [error, setError] = useState("");

  const t = useTranslations('pages.tools.tools.ipLookup');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  const validateIP = (ip: string): boolean => {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  };

  const lookupIP = async () => {
    if (!ipAddress.trim()) {
      setError(t('status.enterIP'));
      return;
    }

    if (!validateIP(ipAddress.trim())) {
      setError(t('status.invalidIP'));
      return;
    }

    setError("");
    setIpInfo(null);

    const ip = ipAddress.trim();
    
    // 多个IP查询源，按优先级排序
    const lookupSources = [
      {
        name: 'IP-API',
        url: `http://ip-api.com/json/${ip}?lang=${locale === 'zh' ? 'zh-CN' : 'en'}`,
        parser: (data: IPAPIResponse) => {
          if (data.status === 'fail') {
            throw new Error(data.message || t('status.error'));
          }
          return {
            ip: data.query,
            country: data.country,
            region: data.regionName,
            city: data.city,
            isp: data.isp,
            org: data.org,
            timezone: data.timezone,
            lat: data.lat || 0,
            lon: data.lon || 0,
            as: data.as || 'N/A'
          };
        }
      },
      {
        name: 'IPapi.co',
        url: `https://ipapi.co/${ip}/json/`,
        parser: (data: IPApiCoResponse) => {
          if (data.error) {
            throw new Error(data.reason || t('errors.ipLookupFailed'));
          }
          return {
            ip: data.ip,
            country: data.country_name || data.country,
            region: data.region || data.region_code,
            city: data.city,
            isp: data.org || data.asn,
            org: data.org || data.asn,
            timezone: data.timezone,
            lat: data.latitude || 0,
            lon: data.longitude || 0,
            as: data.asn || 'N/A'
          };
        }
      },
      {
        name: 'IPinfo.io',
        url: `https://ipinfo.io/${ip}/json`,
        parser: (data: IPInfoResponse) => {
          if (data.error) {
            throw new Error(data.error.message || t('errors.ipLookupFailed'));
          }
          const [lat, lon] = (data.loc || '0,0').split(',').map(Number);
          return {
            ip: data.ip,
            country: data.country,
            region: data.region,
            city: data.city,
            isp: data.org || 'N/A',
            org: data.org || 'N/A',
            timezone: data.timezone || 'N/A',
            lat: lat || 0,
            lon: lon || 0,
            as: 'N/A'
          };
        }
      }
    ];

    for (const source of lookupSources) {
      try {
        const response = await fetch(source.url);
        const data = await response.json();
        
        const parsedData = source.parser(data);
        if (parsedData.ip) {
          setIpInfo(parsedData as IPInfo);

          return;
        }
      } catch {
          console.log(`${source.name} lookup failed`);
          continue;
        }
    }
    
    setError(t('status.error'));

  };

  const getWebRTCIP = async () => {
    return new Promise<string>((resolve, reject) => {
      try {
        // 创建RTCPeerConnection对象
        const RTCPeerConnectionClass = window.RTCPeerConnection || 
                                      (window as unknown as { mozRTCPeerConnection?: typeof RTCPeerConnection }).mozRTCPeerConnection || 
                                      (window as unknown as { webkitRTCPeerConnection?: typeof RTCPeerConnection }).webkitRTCPeerConnection;
        
        if (!RTCPeerConnectionClass) {
          reject(new Error('WebRTC not supported'));
          return;
        }

        const pc = new RTCPeerConnectionClass({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' }
          ]
        });

        const localIPs = new Set<string>();
        const ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;

        const processCandidate = (candidate: string) => {
          const matches = candidate.match(ipRegex);
          if (matches) {
            matches.forEach(ip => {
              // 过滤掉本地IP和无效IP
              if (!ip.startsWith('192.168.') && 
                  !ip.startsWith('10.') && 
                  !ip.startsWith('172.') &&
                  !ip.startsWith('127.') &&
                  !ip.startsWith('169.254.') &&
                  ip !== '0.0.0.0') {
                localIPs.add(ip);
              }
            });
          }
        };

        // 创建数据通道
        pc.createDataChannel('');

        // 处理ICE候选
        pc.onicecandidate = (event) => {
          if (event.candidate && event.candidate.candidate) {
            processCandidate(event.candidate.candidate);
          }
        };

        // 创建offer
        pc.createOffer()
          .then(offer => {
            // 从SDP中提取IP
            if (offer.sdp) {
              offer.sdp.split('\n').forEach(line => {
                if (line.indexOf('candidate') >= 0) {
                  processCandidate(line);
                }
              });
            }
            return pc.setLocalDescription(offer);
          })
          .catch(reject);

        // 设置超时
        setTimeout(() => {
          pc.close();
          if (localIPs.size > 0) {
            const realIP = Array.from(localIPs)[0];
            setWebrtcIP(realIP);
            resolve(realIP);
          } else {
            reject(new Error('No real IP found'));
          }
        }, 3000);

      } catch (err) {
        reject(err);
      }
    });
  };

  const getLocalExitIP = useCallback(async () => {
    try {
      // 使用简单的IP获取服务来获取本地出口IP
      const ipSources = [
        'https://api.ipify.org?format=json',
        'https://ipapi.co/ip/',
        'https://icanhazip.com',
        'https://ident.me'
      ];

      for (const source of ipSources) {
        try {
          const response = await fetch(source);
          let ip = '';
          
          if (source.includes('json')) {
            const data = await response.json();
            ip = data.ip;
          } else {
            ip = (await response.text()).trim();
          }
          
          if (ip && validateIP(ip)) {
            setLocalExitIP(ip);
            return ip;
          }
        } catch {
          console.log(`Failed to get IP from ${source}`);
          continue;
        }
      }
    } catch {
      console.error('Failed to get local exit IP');
    }
    return null;
  }, []);

  const getCurrentIP = useCallback(async () => {

    setError("");
    setIpInfo(null);

    // 多个IP获取源，按优先级排序
    const ipSources = [
      {
        name: 'PConline',
        url: 'https://whois.pconline.com.cn/ipJson.jsp?json=true',
        parser: (data: PConlineResponse) => ({
          ip: data.ip,
          country: locale === 'zh' ? '中国' : 'China',
          region: data.pro,
          city: data.city,
          isp: data.addr?.split(' ').pop() || 'N/A',
          org: data.addr || 'N/A',
          timezone: 'Asia/Shanghai',
          lat: 0,
          lon: 0,
          as: 'N/A'
        })
      },
      {
        name: 'VORE API',
        url: 'https://api.vore.top/api/IPdata',
        parser: (data: VoreAPIResponse) => ({
          ip: data.ipinfo?.text || '',
          country: data.ipdata?.info1 === '广东省' ? (locale === 'zh' ? '中国' : 'China') : (data.ipdata?.info1 || 'N/A'),
          region: data.ipdata?.info1 || 'N/A',
          city: data.ipdata?.info2 || 'N/A',
          isp: data.ipdata?.isp || 'N/A',
          org: data.ipdata?.isp || 'N/A',
          timezone: 'Asia/Shanghai',
          lat: 0,
          lon: 0,
          as: 'N/A'
        })
      },
      {
        name: 'IPapi.co',
        url: 'https://ipapi.co/json/',
        parser: (data: IPApiCoResponse) => ({
          ip: data.ip,
          country: data.country_name || data.country,
          region: data.region || data.region_code,
          city: data.city,
          isp: data.org || data.asn,
          org: data.org || data.asn,
          timezone: data.timezone,
          lat: data.latitude || 0,
          lon: data.longitude || 0,
          as: data.asn || 'N/A'
        })
      },
      {
        name: 'IP-API',
        url: 'http://ip-api.com/json/?lang=zh-CN',
        parser: (data: IPAPIResponse) => ({
          ip: data.query,
          country: data.country,
          region: data.regionName,
          city: data.city,
          isp: data.isp,
          org: data.org,
          timezone: data.timezone,
          lat: data.lat || 0,
          lon: data.lon || 0,
          as: data.as || 'N/A'
        })
      },
      {
        name: 'IPify',
        url: 'https://api.ipify.org?format=json',
        parser: (data: IPifyResponse) => ({ ip: data.ip })
      }
    ];

    for (const source of ipSources) {
      try {
        const response = await fetch(source.url);
        const data = await response.json();
        
        if (data && (data.ip || data.query || data.ipinfo?.text)) {
          const parsedData = source.parser(data);
          
          if (parsedData.ip) {
            setIpAddress(parsedData.ip);
            
            // 如果获取到了完整信息，直接设置
             if ('country' in parsedData && parsedData.country && parsedData.country !== 'N/A') {
               setIpInfo(parsedData as IPInfo);
               return;
             } else {
               // 只获取到IP，需要查询详细信息
               await lookupIPDetails(parsedData.ip);
               return;
             }
          }
        }
      } catch {
          console.log(`${source.name} failed`);
          continue;
        }
    }
    
    setError(t('status.error'));
  }, []);

  const lookupIPDetails = async (ip: string) => {
    // 多个IP查询源，按优先级排序
    const lookupSources = [
      {
        name: 'IP-API',
        url: `http://ip-api.com/json/${ip}?lang=${locale === 'zh' ? 'zh-CN' : 'en'}`,
        parser: (data: IPAPIResponse) => {
          if (data.status === 'fail') {
            throw new Error(data.message || t('errors.ipLookupFailed'));
          }
          return {
            ip: data.query,
            country: data.country,
            region: data.regionName,
            city: data.city,
            isp: data.isp,
            org: data.org,
            timezone: data.timezone,
            lat: data.lat || 0,
            lon: data.lon || 0,
            as: data.as || 'N/A'
          };
        }
      },
      {
        name: 'IPapi.co',
        url: `https://ipapi.co/${ip}/json/`,
        parser: (data: IPApiCoResponse) => {
          if (data.error) {
            throw new Error(data.reason || t('errors.ipLookupFailed'));
          }
          return {
            ip: data.ip,
            country: data.country_name || data.country,
            region: data.region || data.region_code,
            city: data.city,
            isp: data.org || data.asn,
            org: data.org || data.asn,
            timezone: data.timezone,
            lat: data.latitude || 0,
            lon: data.longitude || 0,
            as: data.asn || 'N/A'
          };
        }
      },
      {
        name: 'IPinfo.io',
        url: `https://ipinfo.io/${ip}/json`,
        parser: (data: IPInfoResponse) => {
          if (data.error) {
            throw new Error(data.error.message || t('errors.ipLookupFailed'));
          }
          const [lat, lon] = (data.loc || '0,0').split(',').map(Number);
          return {
            ip: data.ip,
            country: data.country,
            region: data.region,
            city: data.city,
            isp: data.org || 'N/A',
            org: data.org || 'N/A',
            timezone: data.timezone || 'N/A',
            lat: lat || 0,
            lon: lon || 0,
            as: 'N/A'
          };
        }
      }
    ];

    for (const source of lookupSources) {
      try {
        const response = await fetch(source.url);
        const data = await response.json();
        
        const parsedData = source.parser(data);
        if (parsedData.ip) {
          setIpInfo(parsedData as IPInfo);
          return;
        }
      } catch {
          console.log(`${source.name} lookup failed`);
          continue;
        }
    }
    
    console.error('All IP lookup services failed');
  };

  // 页面加载时自动获取当前IP、本地出口IP和WebRTC真实IP
  useEffect(() => {
    getCurrentIP();
    getLocalExitIP();
    // 尝试获取WebRTC真实IP
    getWebRTCIP().catch(() => {
      console.log('WebRTC IP detection failed');
    });
  }, [getCurrentIP, getLocalExitIP]);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/ip-lookup')}`,
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": locale === 'zh' ? "CNY" : "USD"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                    {t('breadcrumb.tools')}
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl mb-6 shadow-lg">
              <span className="text-2xl text-white">🌐</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              {t('description')}
            </p>
          </div>

          {/* Main Content */}
          <div className="max-w-4xl mx-auto">
            {/* Input Section */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl border border-gray-200 dark:border-slate-700 p-8 mb-8">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    {t('input.label')}
                  </label>
                  <input
                    type="text"
                    value={ipAddress}
                    onChange={(e) => setIpAddress(e.target.value)}
                    placeholder={t('input.placeholder')}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-700 dark:text-white transition-all duration-200"
                    onKeyPress={(e) => e.key === 'Enter' && lookupIP()}
                  />
                </div>

                {error && (
                  <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
                    <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                  </div>
                )}
              </div>
            </div>

            {/* IP Information Section */}
            {(localExitIP || webrtcIP) && (
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl border border-gray-200 dark:border-slate-700 p-6 mb-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <span className="mr-3">🌐</span>
{t('ipInfo.title')}
                </h2>
                
                <div className="space-y-4">
                  {localExitIP && (
                    <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-semibold text-green-600 dark:text-green-400 mb-1">{t('ipInfo.currentExitIP')}</h3>
                          <p className="text-2xl font-mono font-bold text-green-700 dark:text-green-300">{localExitIP}</p>
                          <p className="text-sm text-green-600 dark:text-green-400 mt-1">{t('ipInfo.currentExitIPDesc')}</p>
                        </div>
                        <div className="text-4xl">
                          🔗
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {webrtcIP && (
                    <div className="p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-1">{t('ipInfo.realIP')}</h3>
                          <p className="text-2xl font-mono font-bold text-purple-700 dark:text-purple-300">{webrtcIP}</p>
                          <p className="text-sm text-purple-600 dark:text-purple-400 mt-1">{t('ipInfo.realIPDesc')}</p>
                        </div>
                        <div className="text-4xl">
                          🔍
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {localExitIP && webrtcIP && localExitIP !== webrtcIP && (
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                      <p className="text-sm text-yellow-700 dark:text-yellow-300">
{t('ipInfo.vpnWarning')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Results Section */}
            {ipInfo && (
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl border border-gray-200 dark:border-slate-700 p-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                  <span className="mr-3">📍</span>
{t('details.title')}
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.ipAddress')}</h3>
                      <p className="text-lg font-mono text-gray-900 dark:text-white">{ipInfo.ip}</p>
                    </div>
                    
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.location')}</h3>
                      <p className="text-lg text-gray-900 dark:text-white">
                        {ipInfo.country} {ipInfo.region} {ipInfo.city}
                      </p>
                    </div>
                    
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.ispInfo')}</h3>
                      <p className="text-lg text-gray-900 dark:text-white">{ipInfo.isp}</p>
                    </div>
                    
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.organization')}</h3>
                      <p className="text-lg text-gray-900 dark:text-white">{ipInfo.org}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.timezone')}</h3>
                      <p className="text-lg text-gray-900 dark:text-white">{ipInfo.timezone}</p>
                    </div>
                    
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.coordinates')}</h3>
                      <p className="text-lg font-mono text-gray-900 dark:text-white">
                        {ipInfo.lat.toFixed(4)}, {ipInfo.lon.toFixed(4)}
                      </p>
                    </div>
                    
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-xl">
                      <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">{t('details.asInfo')}</h3>
                      <p className="text-lg text-gray-900 dark:text-white">{ipInfo.as}</p>
                    </div>
                    
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                      <h3 className="text-sm font-semibold text-blue-600 dark:text-blue-400 mb-2">{t('details.mapLink')}</h3>
                      <a 
                        href={`https://www.google.com/maps?q=${ipInfo.lat},${ipInfo.lon}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline"
                      >
{t('details.viewOnGoogleMaps')}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Features */}
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🔒</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('features.privacyTitle')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('features.privacyDesc')}</p>
              </div>
              
              <div className="text-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('features.fastTitle')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('features.fastDesc')}</p>
              </div>
              
              <div className="text-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🌍</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('features.globalTitle')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('features.globalDesc')}</p>
              </div>
            </div>
          </div>
        </div></div>
    </>
  );
}