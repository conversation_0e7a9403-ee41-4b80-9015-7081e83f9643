import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.unitConverter' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const getHreflangAlternates = (basePath: string) => {
    return {
      'zh': basePath,
      'en': `/en${basePath}`,
    };
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/unit-converter');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "单位转换",
      "计量单位",
      "长度转换",
      "重量转换",
      "面积转换",
      "体积转换",
      "温度转换",
      "速度转换",
      "米转换",
      "千克转换",
      "摄氏度转换",
      "华氏度转换",
      "英寸转换",
      "英尺转换",
      "磅转换",
      "单位换算",
      "度量衡转换",
      "国际单位制"
    ] : [
      "unit conversion",
      "measurement units",
      "length conversion",
      "weight conversion",
      "area conversion",
      "volume conversion",
      "temperature conversion",
      "speed conversion",
      "meter conversion",
      "kilogram conversion",
      "celsius conversion",
      "fahrenheit conversion",
      "inch conversion",
      "foot conversion",
      "pound conversion",
      "unit calculator",
      "metric conversion",
      "international system"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: getHreflangAlternates('/tools/unit-converter'),
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools/unit-converter-og.jpg",
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools/unit-converter-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function UnitConverterLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}