import { Metadata } from 'next';
import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import { defaultLocale } from '@/i18n';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.mysqlHashGenerator' });
  
  const title = t('meta.title');
  const description = t('meta.description');
  const keywords = t('meta.keywords');
  
  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };
  
  const canonicalUrl = `${SITE_URL}${getLocalizedHref('/tools/mysql-hash-generator')}`;
  
  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      siteName: SITE_NAME,
      locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'zh': `${SITE_URL}/tools/mysql-hash-generator`,
        'en': `${SITE_URL}/en/tools/mysql-hash-generator`,
      },
    },
  };
}

export default function MySQLHashGeneratorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}