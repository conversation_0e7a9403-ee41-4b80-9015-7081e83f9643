"use client";
import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import { getAllTools } from '@/lib/tools-config';

export default function ToolsPage() {
  const t = useTranslations('pages.tools');
  const tCategories = useTranslations('pages.tools.categories');
  const tList = useTranslations('pages.tools.list');
  const tSite = useTranslations('site');
  const locale = useLocale();
  
  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  const tools = getAllTools();

  const categories = Array.from(new Set(tools.map(tool => tool.categoryKey)));
  
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isSticky, setIsSticky] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  
  // 搜索和分类过滤逻辑
  const filteredTools = tools.filter(tool => {
    // 分类过滤
    const categoryMatch = selectedCategory ? tool.categoryKey === selectedCategory : true;
    
    // 搜索过滤
    if (!searchQuery.trim()) {
      return categoryMatch;
    }
    
    const query = searchQuery.toLowerCase();
    const titleMatch = tList(tool.titleKey).toLowerCase().includes(query);
    const descriptionMatch = tList(tool.descriptionKey).toLowerCase().includes(query);
    const categoryNameMatch = tCategories(tool.categoryKey).toLowerCase().includes(query);
    
    return categoryMatch && (titleMatch || descriptionMatch || categoryNameMatch);
  });

  // 动态设置页面标题
  useEffect(() => {
    let title = `${t('title')} - ${t('description')} | ${tSite('name')}`;
    
    if (searchQuery.trim()) {
      title = `搜索: ${searchQuery} - ${t('title')} | ${tSite('name')}`;
    } else if (selectedCategory) {
      title = `${tCategories(selectedCategory)} - ${t('title')} | ${tSite('name')}`;
    }
    
    document.title = title;
  }, [selectedCategory, searchQuery, t, tCategories, tSite]);

  // 监听滚动，控制左侧导航的粘性定位
  useEffect(() => {
    const handleScroll = () => {
      if (headerRef.current) {
        const headerBottom = headerRef.current.offsetTop + headerRef.current.offsetHeight;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        setIsSticky(scrollTop > headerBottom);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": t('title'),
    "description": t('description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools')}`,
    "mainEntity": {
      "@type": "ItemList",
      "name": t('title'),
      "itemListElement": tools.map((tool, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "SoftwareApplication",
          "name": tList(tool.titleKey),
          "description": tList(tool.descriptionKey),
          "url": `${SITE_URL}${getLocalizedHref(tool.href)}`,
          "applicationCategory": "DeveloperApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": locale === 'zh' ? 'CNY' : 'USD'
          }
        }
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": locale === 'zh' ? '首页' : 'Home',
          "item": SITE_URL
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": t('title'),
          "item": `${SITE_URL}${getLocalizedHref('/tools')}`
        }
      ]
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="min-h-screen bg-white dark:bg-slate-900">

        {/* Header Section */}
        <div ref={headerRef} className="bg-white dark:bg-slate-900 border-b border-gray-200 dark:border-slate-700">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                {t('page.title')}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-4xl mx-auto">
                {t('page.subtitle', { count: selectedCategory ? filteredTools.length : tools.length })}
                {selectedCategory && (
                  <span className="block text-base mt-2 text-blue-600 dark:text-blue-400">
                    {t('page.currentCategory', { category: tCategories(selectedCategory) })}
                  </span>
                )}
              </p>
              
              {/* 搜索框 */}
              <div className="max-w-md mx-auto mb-8">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-slate-600 rounded-xl leading-5 bg-white dark:bg-slate-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-colors"
                    placeholder={t('search.placeholder')}
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <span className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-3 py-1 rounded-full border border-blue-200 dark:border-blue-800">{t('page.features.privacy')}</span>
                <span className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-3 py-1 rounded-full border border-green-200 dark:border-green-800">{t('page.features.speed')}</span>
                <span className="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 px-3 py-1 rounded-full border border-purple-200 dark:border-purple-800">{t('page.features.free')}</span>
                <span className="bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-3 py-1 rounded-full border border-orange-200 dark:border-orange-800">{t('page.features.responsive')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Category Selector */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-8 lg:hidden">
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700 p-4 mb-6">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">{t('page.categoryTitle')}</h3>
            <div className="flex flex-wrap gap-2">
              <button 
                onClick={() => setSelectedCategory(null)}
                className={`px-3 py-2 text-sm rounded-lg font-medium transition-colors ${
                  selectedCategory === null 
                    ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                    : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 bg-gray-50 dark:bg-slate-700'
                }`}
              >
                {t('page.allTools')} ({tools.length})
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                    selectedCategory === category 
                      ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 font-medium' 
                      : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 bg-gray-50 dark:bg-slate-700'
                  }`}
                >
                  {tCategories(category)} ({tools.filter((tool) => tool.categoryKey === category).length})
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tools Grid */}
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:pt-12 relative min-h-[600px]">
          {/* Floating Category Navigation */}
          <div className={`z-20 hidden lg:block ${
            isSticky 
              ? 'fixed top-25' 
              : 'absolute top-8 left-7 '
          }`} style={{
            left: isSticky ? 'clamp(1rem, calc((100vw - 80rem) / 2 + 1.5rem), calc(50vw - 6rem))' : undefined
          }}>
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700 p-4 w-48">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">{t('page.categoryTitle')}</h3>
              <div className="space-y-2">
                <button 
                  onClick={() => setSelectedCategory(null)}
                  className={`w-full text-left px-3 py-2 text-sm rounded-lg font-medium transition-colors ${
                    selectedCategory === null 
                      ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                      : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                  }`}
                >
                  {t('page.allToolsWithCount', { count: tools.length })}
                </button>
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-colors ${
                      selectedCategory === category 
                        ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 font-medium' 
                        : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    }`}
                  >
                    {tCategories(category)} ({tools.filter((tool) => tool.categoryKey === category).length})
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:ml-56">
            {filteredTools.length === 0 ? (
              <div className="col-span-full flex flex-col items-center justify-center py-16 text-center">
                <div className="w-24 h-24 bg-gray-100 dark:bg-slate-700 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {t('search.noResults')}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-sm">
                  {t('search.noResultsDescription')}
                </p>
                {(searchQuery || selectedCategory) && (
                  <div className="flex flex-wrap gap-3">
                    {searchQuery && (
                       <button
                         onClick={() => setSearchQuery('')}
                         className="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors text-sm font-medium"
                       >
                         {t('search.clearSearch')}
                       </button>
                     )}
                     {selectedCategory && (
                       <button
                         onClick={() => setSelectedCategory(null)}
                         className="px-4 py-2 bg-gray-100 dark:bg-slate-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors text-sm font-medium"
                       >
                         {t('search.viewAllCategories')}
                       </button>
                     )}
                  </div>
                )}
              </div>
            ) : (
              filteredTools.map((tool) => (
              <Link
                key={tool.id}
                href={getLocalizedHref(tool.href)}
                className="group relative bg-white dark:bg-slate-800 rounded-xl p-4 shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 hover:-translate-y-0.5 overflow-hidden"
              >
                {/* Top accent line */}
                <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div className="relative">
                  {/* Header section */}
                  <div className="flex items-start gap-3 mb-3">
                    <div className="flex-shrink-0">
                       <div className="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                         <span className="text-xl">{tool.icon}</span>
                       </div>
                     </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="h-12 flex items-start">
                        <h3 className="font-semibold text-base text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200 line-clamp-2 leading-6">
                          {tList(tool.titleKey)}
                        </h3>
                      </div>
                      <span className="inline-block text-xs px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded font-medium">
                        {tCategories(tool.categoryKey)}
                      </span>
                    </div>
                    
                    {/* Status indicator */}
                    <div className="flex-shrink-0">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    </div>
                  </div>
                  
                  {/* Description */}
                  <p className="text-sm text-gray-600 dark:text-gray-400 leading-5 mb-3 line-clamp-2">
                    {tList(tool.descriptionKey)}
                  </p>
                  
                  {/* Bottom section */}
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                      <span>{t('page.toolStatus.online')}</span>
                      <span>•</span>
                      <span>{t('page.toolStatus.free')}</span>
                    </div>
                    
                    <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium opacity-0 group-hover:opacity-100 transition-all duration-200">
                      {t('page.toolStatus.preview')}
                    </button>
                  </div>
                </div>
              </Link>
              ))
            )}
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {t('page.whyChoose.title')}
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                {t('page.whyChoose.subtitle')}
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🔒</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('page.whyChoose.privacy.title')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('page.whyChoose.privacy.description')}</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('page.whyChoose.speed.title')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('page.whyChoose.speed.description')}</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('page.whyChoose.professional.title')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('page.whyChoose.professional.description')}</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">💯</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('page.whyChoose.free.title')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{t('page.whyChoose.free.description')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}