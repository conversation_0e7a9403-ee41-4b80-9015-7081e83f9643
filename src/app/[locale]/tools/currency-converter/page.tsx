"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_URL } from "@/lib/constants";

// 主要货币列表
const currencies = [
  { code: "USD", name: "美元", symbol: "$", flag: "🇺🇸" },
  { code: "EUR", name: "欧元", symbol: "€", flag: "🇪🇺" },
  { code: "GBP", name: "英镑", symbol: "£", flag: "🇬🇧" },
  { code: "JPY", name: "日元", symbol: "¥", flag: "🇯🇵" },
  { code: "CNY", name: "人民币", symbol: "¥", flag: "🇨🇳" },
  { code: "KRW", name: "韩元", symbol: "₩", flag: "🇰🇷" },
  { code: "HKD", name: "港币", symbol: "HK$", flag: "🇭🇰" },
  { code: "TWD", name: "新台币", symbol: "NT$", flag: "🇹🇼" },
  { code: "SGD", name: "新加坡元", symbol: "S$", flag: "🇸🇬" },
  { code: "AUD", name: "澳元", symbol: "A$", flag: "🇦🇺" },
  { code: "CAD", name: "加元", symbol: "C$", flag: "🇨🇦" },
  { code: "CHF", name: "瑞士法郎", symbol: "CHF", flag: "🇨🇭" },
  { code: "SEK", name: "瑞典克朗", symbol: "kr", flag: "🇸🇪" },
  { code: "NOK", name: "挪威克朗", symbol: "kr", flag: "🇳🇴" },
  { code: "DKK", name: "丹麦克朗", symbol: "kr", flag: "🇩🇰" },
  { code: "RUB", name: "俄罗斯卢布", symbol: "₽", flag: "🇷🇺" },
  { code: "INR", name: "印度卢比", symbol: "₹", flag: "🇮🇳" },
  { code: "BRL", name: "巴西雷亚尔", symbol: "R$", flag: "🇧🇷" },
  { code: "MXN", name: "墨西哥比索", symbol: "$", flag: "🇲🇽" },
  { code: "ZAR", name: "南非兰特", symbol: "R", flag: "🇿🇦" },
];

interface ExchangeRates {
  [key: string]: number;
}

export default function CurrencyConverterPage() {
  const [fromCurrency, setFromCurrency] = useState("USD");
  const [toCurrency, setToCurrency] = useState("CNY");
  const [amount, setAmount] = useState("1");
  const [result, setResult] = useState("");
  const [exchangeRates, setExchangeRates] = useState<ExchangeRates>({});
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [error, setError] = useState("");

  const t = useTranslations('pages.tools.tools.currencyConverter');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 获取本地化的货币名称
  const getCurrencyName = (code: string) => {
    return t(`currencies.${code}`);
  };

  // 获取汇率数据
  const fetchExchangeRates = useCallback(async () => {
    setLoading(true);
    setError("");
    
    try {
      // 使用免费的汇率API
      const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${fromCurrency}`);
      
      if (!response.ok) {
        throw new Error(t('converter.error'));
      }
      
      const data = await response.json();
      setExchangeRates(data.rates);
      setLastUpdated(new Date(data.date));
    } catch {
      setError(t('converter.error'));
      
      // 使用模拟数据作为备用
      const mockRates: ExchangeRates = {
        USD: 1,
        EUR: 0.85,
        GBP: 0.73,
        JPY: 110,
        CNY: 6.45,
        KRW: 1180,
        HKD: 7.8,
        TWD: 28,
        SGD: 1.35,
        AUD: 1.35,
        CAD: 1.25,
        CHF: 0.92,
        SEK: 8.5,
        NOK: 8.8,
        DKK: 6.3,
        RUB: 75,
        INR: 74,
        BRL: 5.2,
        MXN: 20,
        ZAR: 14.5,
      };
      setExchangeRates(mockRates);
      setLastUpdated(new Date());
    } finally {
      setLoading(false);
    }
  }, [fromCurrency]);

  // 计算转换结果
  const calculateConversion = useCallback(() => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount < 0) {
      setResult("");
      return;
    }

    if (fromCurrency === toCurrency) {
      setResult(numAmount.toFixed(2));
      return;
    }

    const rate = exchangeRates[toCurrency];
    if (rate) {
      const convertedAmount = numAmount * rate;
      setResult(convertedAmount.toFixed(2));
    }
  }, [amount, fromCurrency, toCurrency, exchangeRates]);

  // 交换货币
  const swapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
  };

  // 初始化和监听变化
  useEffect(() => {
    fetchExchangeRates();
  }, [fetchExchangeRates]);

  useEffect(() => {
    calculateConversion();
  }, [calculateConversion]);

  const fromCurrencyInfo = currencies.find(c => c.code === fromCurrency);
  const toCurrencyInfo = currencies.find(c => c.code === toCurrency);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/currency-converter')}`,
    "applicationCategory": "FinanceApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": locale === 'zh' ? "CNY" : "USD"
    },
    "featureList": locale === 'zh' ? [
      "实时汇率查询",
      "多种货币转换",
      "历史汇率数据",
      "货币符号显示"
    ] : [
      "Real-time exchange rate queries",
      "Multi-currency conversion",
      "Historical exchange rate data",
      "Currency symbol display"
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-20">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                    {t('breadcrumb.tools')}
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* 主要转换区域 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mb-8">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-end">
                {/* 源货币 */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('converter.from')}
                  </label>
                  <select
                    value={fromCurrency}
                    onChange={(e) => setFromCurrency(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.flag} {currency.code} - {getCurrencyName(currency.code)}
                      </option>
                    ))}
                  </select>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder={t('converter.amount')}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                    min="0"
                    step="0.01"
                  />
                </div>

                {/* 交换按钮 */}
                <div className="flex justify-center">
                  <button
                    onClick={swapCurrencies}
                    className="p-3 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors duration-200 shadow-lg hover:shadow-xl"
                    title={t('converter.swap')}
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                    </svg>
                  </button>
                </div>

                {/* 目标货币 */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('converter.to')}
                  </label>
                  <select
                    value={toCurrency}
                    onChange={(e) => setToCurrency(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.flag} {currency.code} - {getCurrencyName(currency.code)}
                      </option>
                    ))}
                  </select>
                  <div className="px-4 py-3 bg-gray-50 dark:bg-slate-600 rounded-lg border border-gray-200 dark:border-slate-500">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {loading ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-500 mr-2"></div>
                          {t('converter.loading')}
                        </div>
                      ) : (
                        `${toCurrencyInfo?.symbol || ""}${result || "0.00"}`
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* 转换结果显示 */}
              {result && !loading && (
                <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="text-center">
                    <p className="text-lg text-gray-700 dark:text-gray-300">
                      <span className="font-semibold">
                        {fromCurrencyInfo?.symbol || ""}{amount} {fromCurrency}
                      </span>
                      {" = "}
                      <span className="font-bold text-green-600 dark:text-green-400">
                        {toCurrencyInfo?.symbol || ""}{result} {toCurrency}
                      </span>
                    </p>
                    {exchangeRates[toCurrency] && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {t('converter.rate')}: 1 {fromCurrency} = {exchangeRates[toCurrency].toFixed(4)} {toCurrency}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* 错误提示 */}
              {error && (
                <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  <p className="text-red-600 dark:text-red-400 text-center">{error}</p>
                </div>
              )}

              {/* 更新时间 */}
              {lastUpdated && (
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {t('converter.lastUpdate')}: {lastUpdated.toLocaleString(locale === 'zh' ? "zh-CN" : "en-US")}
                  </p>
                  <button
                    onClick={fetchExchangeRates}
                    disabled={loading}
                    className="mt-2 px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200 disabled:opacity-50"
                  >
                    {loading ? t('converter.loading') : t('converter.convert')}
                  </button>
                </div>
              )}
            </div>

            {/* 常用汇率表格 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {t('popular.title')} ({t('popular.pairs')} {fromCurrency})
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {currencies
                  .filter(currency => currency.code !== fromCurrency)
                  .slice(0, 9)
                  .map((currency) => (
                    <div
                      key={currency.code}
                      className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg border border-gray-200 dark:border-slate-600 hover:border-green-300 dark:hover:border-green-600 transition-colors duration-200 cursor-pointer"
                      onClick={() => setToCurrency(currency.code)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-xl">{currency.flag}</span>
                          <div>
                            <p className="font-semibold text-gray-900 dark:text-white">
                              {currency.code}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {getCurrencyName(currency.code)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-900 dark:text-white">
                            {exchangeRates[currency.code] 
                              ? exchangeRates[currency.code].toFixed(4)
                              : "---"
                            }
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            {/* 使用说明 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mt-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {t('instructions.title')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {t('features.title')}
                  </h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    {[0, 1, 2, 3].map((index) => {
                      const item = t.raw(`features.items.${index}`);
                      return item ? (
                        <li key={index} className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          {item}
                        </li>
                      ) : null;
                    }).filter(Boolean)}
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {t('usage.title')}
                  </h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    {[0, 1, 2, 3].map((index) => {
                      const item = t.raw(`usage.items.${index}`);
                      return item ? (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-500 mr-2">{index + 1}.</span>
                          {item}
                        </li>
                      ) : null;
                    }).filter(Boolean)}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div></div>
    </>
  );
}