'use client';

import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';
import Link from 'next/link';
import { ChevronRight, Home, Zap, Settings, Globe, Shield, Download, Github, ExternalLink, CheckCircle, Star, Users, Code, Rocket } from 'lucide-react';
import { defaultLocale } from '@/i18n';

export default function AiGitCommitPage() {
  const t = useTranslations('pages.tools.tools.aiGitCommit');
  const locale = useLocale();

  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  const features = [
    {
      icon: <Zap className="h-6 w-6" />,
      title: t('features.aiGeneration'),
      description: t('features.aiGeneration') === 'AI Smart Generation' ? 'Support for mainstream AI services like OpenAI, Claude, Gemini, <PERSON>yi Qianwen' : '支持 OpenAI、<PERSON>、<PERSON>、通义千问等主流 AI 服务'
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: t('features.localGeneration'),
      description: t('features.localGeneration') === 'Local Rule Generation' ? 'Local intelligent analysis without API, completely offline available' : '无需 API 的本地智能分析，完全离线可用'
    },
    {
      icon: <Settings className="h-6 w-6" />,
      title: t('features.oneClick'),
      description: t('features.oneClick') === 'One-Click Operation' ? 'Click the ✨ button in the source control title bar to generate' : '点击源码管理标题栏的 ✨ 按钮即可生成'
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: t('features.smartFallback'),
      description: t('features.smartFallback') === 'Smart Fallback Mechanism' ? 'Automatically use local generation when AI fails, ensuring functionality is always available' : 'AI 失败时自动使用本地生成，确保功能始终可用'
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: t('features.multiLanguage'),
      description: t('features.multiLanguage') === 'Multi-language Support' ? 'Support for Chinese and English commit message generation' : '支持中文和英文提交信息生成'
    },
    {
      icon: <Star className="h-6 w-6" />,
      title: t('features.multiStyle'),
      description: t('features.multiStyle') === 'Multiple Commit Styles' ? 'Multiple styles including Conventional Commits, concise, detailed, etc.' : 'Conventional Commits、简洁、详细等多种风格'
    }
  ];

  const providers = [
    {
      name: "OpenAI",
      models: "GPT-3.5/GPT-4/GPT-4o",
      description: locale === 'en' ? 'Mature and stable, supports multiple models' : '成熟稳定，支持多种模型',
      color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    },
    {
      name: "Claude",
      models: "Claude-3-Sonnet/Haiku",
      description: locale === 'en' ? 'Safe and reliable, suitable for enterprise use' : '安全可靠，适合企业使用',
      color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    },
    {
      name: "Gemini",
      models: "Gemini-Pro/1.5-Pro/Flash",
      description: locale === 'en' ? 'Google technology, multimodal support' : 'Google 技术，多模态支持',
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    },
    {
      name: locale === 'en' ? 'Tongyi Qianwen' : '通义千问',
      models: "Qwen-Turbo/Plus/Max",
      description: locale === 'en' ? 'Chinese optimized, stable domestic access' : '中文优化，国内访问稳定',
      color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
    }
  ];

  const steps = [
    {
      step: "1",
      title: t('usage.step1') || (locale === 'en' ? 'Modify Code' : '修改代码'),
      description: locale === 'en' ? 'Modify code in Git repository' : '在 Git 仓库中进行代码修改'
    },
    {
      step: "2",
      title: t('usage.step2') || (locale === 'en' ? 'Stage Changes' : '暂存更改'),
      description: locale === 'en' ? 'Run git add . to stage changes' : '运行 git add . 暂存更改'
    },
    {
      step: "3",
      title: t('usage.step3') || (locale === 'en' ? 'Open Source Control' : '打开源码管理'),
      description: locale === 'en' ? 'Press Ctrl+Shift+G to open source control panel' : '按 Ctrl+Shift+G 打开源码管理面板'
    },
    {
      step: "4",
      title: t('usage.step4') || (locale === 'en' ? 'Generate Commit Message' : '生成提交信息'),
      description: locale === 'en' ? 'Click the ✨ button on the right side of the title bar' : '点击标题栏右侧的 ✨ 按钮'
    },
    {
      step: "5",
      title: t('usage.step5') || (locale === 'en' ? 'Review and Commit' : '检查并提交'),
      description: locale === 'en' ? 'Review the generated message and click commit' : '检查生成的信息，点击提交'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container-custom py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm mb-8">
          <Link
            href={getLocalizedHref('/')}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center"
          >
            <Home className="h-4 w-4 mr-1" />
            {t('breadcrumb.home')}
          </Link>
          <ChevronRight className="h-4 w-4 text-gray-400" />
          <Link
            href={getLocalizedHref('/tools')}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            {t('breadcrumb.tools')}
          </Link>
          <ChevronRight className="h-4 w-4 text-gray-400" />
          <span className="text-gray-900 dark:text-white font-medium">
            {t('breadcrumb.current')}
          </span>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-6">
            <Zap className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            {t('title')}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            {t('description')}
          </p>
          
          {/* Demo Image */}
         
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://marketplace.visualstudio.com/items?itemName=lafucode.lafucode-ai-git-commit"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              <Download className="h-5 w-5 mr-2" />
              {t('install.button') || (locale === 'en' ? 'Install Extension' : '安装扩展')}
            </a>
            <a
              href="https://github.com/lafucode/lafucode-ai-git-commit"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors"
            >
              <Github className="h-5 w-5 mr-2" />
              {t('source.button') || (locale === 'en' ? 'View Source' : '查看源码')}
            </a>
          </div>
           <div className="max-w-4xl mx-auto mt-8">
            <img 
              src="/assets/blog/git-ai.png" 
              alt="AI Git Commit 演示截图" 
              className="w-full h-auto rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700"
            />
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
            {t('features.title') || (locale === 'en' ? '✨ Features' : '✨ 功能特性')}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg mr-4">
                    <div className="text-blue-600 dark:text-blue-400">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* AI Providers Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
            {t('providers.title') || (locale === 'en' ? '🤖 Supported AI Services' : '🤖 支持的 AI 服务')}
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            {providers.map((provider, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {provider.name}
                  </h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${provider.color}`}>
                    {provider.models}
                  </span>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  {provider.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Usage Steps */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
            {t('usage.title') || (locale === 'en' ? '🎯 How to Use' : '🎯 使用方法')}
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {steps.map((step, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                  <div className="flex items-start">
                    <div className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full font-bold mr-6 flex-shrink-0">
                      {step.step}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-4">
                        {step.description}
                      </p>
                      {/* 为关键步骤添加演示图片 */}
                      {step.step === "4" && (
                        <div className="mt-4">
                          <img 
                            src="/assets/blog/git-ai2.png" 
                            alt="点击 ✨ 按钮生成提交信息" 
                            className="w-full max-w-md h-auto rounded-lg border border-gray-200 dark:border-gray-600 shadow-md"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Installation Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
            {t('installation.title') || (locale === 'en' ? '📦 Installation' : '📦 安装方式')}
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <Download className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {locale === 'en' ? 'VS Code Marketplace' : 'VS Code 扩展市场'}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {locale === 'en' ? 'Search "LaFu AI Git Commit"' : '搜索 "LaFu AI Git Commit"'}
              </p>
              <a
                href="https://marketplace.visualstudio.com/items?itemName=lafucode.lafucode-ai-git-commit"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline"
              >
                {locale === 'en' ? 'Install Now' : '立即安装'} <ExternalLink className="h-4 w-4 ml-1" />
              </a>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-center">
              <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <Github className="h-6 w-6 text-gray-600 dark:text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {locale === 'en' ? 'GitHub Open Source' : 'GitHub 开源项目'}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {locale === 'en' ? 'View source code and documentation' : '查看源码和文档'}
              </p>
              <a
                href="https://github.com/lafucode/lafucode-ai-git-commit"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline"
              >
                {locale === 'en' ? 'Visit GitHub' : '访问 GitHub'} <ExternalLink className="h-4 w-4 ml-1" />
              </a>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <Globe className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {locale === 'en' ? 'Official Website' : '官方网站'}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {locale === 'en' ? 'More tools and resources' : '更多工具和资源'}
              </p>
              <a
                href="https://lafucode.com"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline"
              >
                {locale === 'en' ? 'Visit Website' : '访问官网'} <ExternalLink className="h-4 w-4 ml-1" />
              </a>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">
            {t('cta.title') || (locale === 'en' ? '🚀 Get Started Now' : '🚀 立即开始使用')}
          </h2>
          <p className="text-xl mb-6 opacity-90">
            {t('cta.description') || (locale === 'en' ? 'Experience AI-powered intelligent commit message generation and boost your development efficiency!' : '体验 AI 驱动的智能提交信息生成，提升你的开发效率！')}
          </p>
          <a
            href="https://marketplace.visualstudio.com/items?itemName=lafucode.lafucode-ai-git-commit"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-bold rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Download className="h-5 w-5 mr-2" />
            {t('cta.button') || (locale === 'en' ? 'Install Extension for Free' : '免费安装扩展')}
          </a>
        </div>
      </div>
    </div>
  );
}