"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from 'next-intl';

export default function CurrentTimeDisplay() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null);
  const t = useTranslations('pages.tools.tools.timestamp');
  const locale = useLocale();

  useEffect(() => {
    // 初始化当前时间（仅在客户端）
    setCurrentTime(new Date());
    
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!currentTime) {
    return (
      <div className="text-center">
        <p className="text-lg opacity-90">Loading current time...</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
      <div>
        <p className="text-sm opacity-90">{t('currentTime.dateTime')}</p>
        <p className="text-xl font-mono">
          {currentTime.toLocaleString(locale === 'zh' ? "zh-CN" : "en-US")}
        </p>
      </div>
      <div>
        <p className="text-sm opacity-90">{t('currentTime.timestampSeconds')}</p>
        <p className="text-xl font-mono">
          {Math.floor(currentTime.getTime() / 1000)}
        </p>
      </div>
      <div>
        <p className="text-sm opacity-90">{t('currentTime.timestampMilliseconds')}</p>
        <p className="text-xl font-mono">
          {currentTime.getTime()}
        </p>
      </div>
    </div>
  );
}
