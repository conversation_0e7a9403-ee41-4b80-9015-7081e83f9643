"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_URL } from "@/lib/constants";
import { Copy, RefreshCw, Shield, Clock, Eye, EyeOff } from "lucide-react";

// 密码强度等级
type PasswordStrength = 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';

// 密码配置接口
interface PasswordConfig {
  length: number;
  includeUppercase: boolean;
  includeLowercase: boolean;
  includeNumbers: boolean;
  includeSymbols: boolean;
  excludeSimilar: boolean;
  excludeAmbiguous: boolean;
}

// 密码分析结果接口
interface PasswordAnalysis {
  strength: PasswordStrength;
  score: number;
  entropy: number;
  crackTime: string;
  feedback: string[];
}

export default function PasswordGeneratorPage() {
  const [config, setConfig] = useState<PasswordConfig>({
    length: 16,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSymbols: true,
    excludeSimilar: false,
    excludeAmbiguous: false,
  });

  const [password, setPassword] = useState('');
  const [analysis, setAnalysis] = useState<PasswordAnalysis | null>(null);
  const [showPassword, setShowPassword] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const t = useTranslations('pages.tools.tools.passwordGenerator');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 获取字符集
  const getCharacterSets = useCallback(() => {
    const sets = {
      uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      lowercase: 'abcdefghijklmnopqrstuvwxyz',
      numbers: '0123456789',
      symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
      similar: 'il1Lo0O',
      ambiguous: '{}[]()/\\\'"`~,;<>.'
    };

    let charset = '';
    if (config.includeUppercase) charset += sets.uppercase;
    if (config.includeLowercase) charset += sets.lowercase;
    if (config.includeNumbers) charset += sets.numbers;
    if (config.includeSymbols) charset += sets.symbols;

    // 排除相似字符
    if (config.excludeSimilar) {
      charset = charset.split('').filter(char => !sets.similar.includes(char)).join('');
    }

    // 排除模糊字符
    if (config.excludeAmbiguous) {
      charset = charset.split('').filter(char => !sets.ambiguous.includes(char)).join('');
    }

    return charset;
  }, [config.includeUppercase, config.includeLowercase, config.includeNumbers, config.includeSymbols, config.excludeAmbiguous, config.excludeSimilar]);

  // 生成密码
  const generatePassword = useCallback(() => {
    setIsGenerating(true);
    
    setTimeout(() => {
      const charset = getCharacterSets();
      if (charset.length === 0) {
        setPassword('');
        setAnalysis(null);
        setIsGenerating(false);
        return;
      }

      let newPassword = '';
      const array = new Uint8Array(config.length);
      crypto.getRandomValues(array);
      
      for (let i = 0; i < config.length; i++) {
        newPassword += charset[array[i] % charset.length];
      }

      setPassword(newPassword);
      setIsGenerating(false);
    }, 100);
  }, [config, getCharacterSets]);

  // 计算密码强度和分析
  const analyzePassword = useCallback((pwd: string): PasswordAnalysis => {
    if (!pwd) {
      return {
        strength: 'very-weak',
        score: 0,
        entropy: 0,
        crackTime: locale === 'zh' ? '瞬间' : 'Instant',
        feedback: [t('feedback.empty')]
      };
    }

    let score = 0;
    const feedback: string[] = [];
    
    // 长度评分
    if (pwd.length >= 12) score += 25;
    else if (pwd.length >= 8) score += 15;
    else if (pwd.length >= 6) score += 5;
    else feedback.push(t('feedback.minLength'));

    // 字符类型评分
    const hasUpper = /[A-Z]/.test(pwd);
    const hasLower = /[a-z]/.test(pwd);
    const hasNumber = /[0-9]/.test(pwd);
    const hasSymbol = /[^A-Za-z0-9]/.test(pwd);
    
    const charTypes = [hasUpper, hasLower, hasNumber, hasSymbol].filter(Boolean).length;
    score += charTypes * 15;
    
    if (!hasUpper) feedback.push(t('feedback.needUpper'));
    if (!hasLower) feedback.push(t('feedback.needLower'));
    if (!hasNumber) feedback.push(t('feedback.needNumber'));
    if (!hasSymbol) feedback.push(t('feedback.needSymbol'));

    // 重复字符检查
    const repeatedChars = pwd.match(/(.)\1{2,}/g);
    if (repeatedChars) {
      score -= 10;
      feedback.push(t('feedback.avoidRepeated'));
    }

    // 常见模式检查
    const commonPatterns = [
      /123456/,
      /abcdef/,
      /qwerty/,
      /password/i,
      /admin/i
    ];
    
    if (commonPatterns.some(pattern => pattern.test(pwd))) {
      score -= 20;
      feedback.push(t('feedback.avoidCommon'));
    }

    // 计算熵值
    const charset = getCharacterSets();
    const entropy = Math.log2(Math.pow(charset.length, pwd.length));
    
    // 计算破解时间
    const crackTime = calculateCrackTime(entropy);
    
    // 确定强度等级
    let strength: PasswordStrength;
    if (score >= 80) strength = 'strong';
    else if (score >= 60) strength = 'good';
    else if (score >= 40) strength = 'fair';
    else if (score >= 20) strength = 'weak';
    else strength = 'very-weak';

    if (feedback.length === 0) {
      feedback.push(t('feedback.good'));
    }

    return {
      strength,
      score: Math.max(0, Math.min(100, score)),
      entropy,
      crackTime,
      feedback
    };
  }, [getCharacterSets]);

  // 计算破解时间
  const calculateCrackTime = (entropy: number): string => {
    // 假设每秒可以尝试10^9次（现代GPU）
    const attemptsPerSecond = 1e9;
    const totalCombinations = Math.pow(2, entropy);
    const averageAttempts = totalCombinations / 2;
    const seconds = averageAttempts / attemptsPerSecond;

    if (seconds < 1) return t('timeUnits.instant');
    if (seconds < 60) return `${Math.round(seconds)} ${t('timeUnits.seconds')}`;
    if (seconds < 3600) return `${Math.round(seconds / 60)} ${t('timeUnits.minutes')}`;
    if (seconds < 86400) return `${Math.round(seconds / 3600)} ${t('timeUnits.hours')}`;
    if (seconds < 31536000) return `${Math.round(seconds / 86400)} ${t('timeUnits.days')}`;
    if (seconds < 31536000000) return `${Math.round(seconds / 31536000)} ${t('timeUnits.years')}`;
    if (seconds < 31536000000000) return `${Math.round(seconds / 31536000000)} ${t('timeUnits.thousandYears')}`;
    return `${Math.round(seconds / 31536000000000)} ${t('timeUnits.millionYears')}`;
  };

  // 复制密码
  const copyPassword = async () => {
    if (!password) return;
    
    try {
      await navigator.clipboard.writeText(password);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch {
      console.error("Copy failed");
    }
  };

  // 获取强度颜色
  const getStrengthColor = (strength: PasswordStrength) => {
    const colors = {
      'very-weak': 'text-red-600 bg-red-100',
      'weak': 'text-orange-600 bg-orange-100',
      'fair': 'text-yellow-600 bg-yellow-100',
      'good': 'text-blue-600 bg-blue-100',
      'strong': 'text-green-600 bg-green-100'
    };
    return colors[strength];
  };

  // 获取强度文本
  const getStrengthText = (strength: PasswordStrength) => {
    return t(`strength.${strength}`);
  };

  // 初始生成密码
  useEffect(() => {
    generatePassword();
  }, [generatePassword]);

  // 分析密码
  useEffect(() => {
    if (password) {
      const result = analyzePassword(password);
      setAnalysis(result);
    }
  }, [password, analyzePassword]);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/password-generator')}`,
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": locale === 'zh' ? "CNY" : "USD"
    },
    "featureList": t.raw('features')
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-12">
          {/* 面包屑导航 */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                    {t('breadcrumb.tools')}
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* 密码配置 */}
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                  <Shield className="w-6 h-6 mr-2 text-purple-600" />
                  {t('config.title')}
                </h2>

                {/* 密码长度 */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('config.length')}: {config.length}
                  </label>
                  <input
                    type="range"
                    min="4"
                    max="128"
                    value={config.length}
                    onChange={(e) => setConfig({...config, length: parseInt(e.target.value)})}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>4</span>
                    <span>128</span>
                  </div>
                </div>

                {/* 字符类型选择 */}
                <div className="space-y-4 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('config.characters')}</h3>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.includeUppercase}
                      onChange={(e) => setConfig({...config, includeUppercase: e.target.checked})}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('config.uppercase')}</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.includeLowercase}
                      onChange={(e) => setConfig({...config, includeLowercase: e.target.checked})}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('config.lowercase')}</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.includeNumbers}
                      onChange={(e) => setConfig({...config, includeNumbers: e.target.checked})}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('config.numbers')}</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.includeSymbols}
                      onChange={(e) => setConfig({...config, includeSymbols: e.target.checked})}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('config.symbols')}</span>
                  </label>
                </div>

                {/* 高级选项 */}
                <div className="space-y-4 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('config.advancedOptions')}</h3>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.excludeSimilar}
                      onChange={(e) => setConfig({...config, excludeSimilar: e.target.checked})}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('config.excludeSimilar')}</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.excludeAmbiguous}
                      onChange={(e) => setConfig({...config, excludeAmbiguous: e.target.checked})}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('config.excludeAmbiguous')}</span>
                  </label>
                </div>

                {/* 生成按钮 */}
                <button
                  onClick={generatePassword}
                  disabled={isGenerating}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center disabled:opacity-50"
                >
                  {isGenerating ? (
                    <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="w-5 h-5 mr-2" />
                  )}
                  {t('actions.generate')}
                </button>
              </div>

              {/* 密码结果和分析 */}
              <div className="space-y-6">
                {/* 生成的密码 */}
                <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{t('result.title')}</h2>
                  
                  <div className="relative">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex-1 relative">
                        <input
                          type={showPassword ? "text" : "password"}
                          value={password}
                          readOnly
                          className="w-full p-4 pr-12 bg-gray-50 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-xl text-gray-900 dark:text-white font-mono text-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                        <button
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                          {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                        </button>
                      </div>
                      <button
                        onClick={copyPassword}
                        className={`p-3 rounded-xl transition-all duration-200 ${
                          copySuccess 
                            ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-600 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-gray-400'
                        }`}
                      >
                        <Copy className="w-5 h-5" />
                      </button>
                    </div>
                    
                    {copySuccess && (
                      <p className="text-sm text-green-600 dark:text-green-400 mb-2">{t('result.copied')}</p>
                    )}
                  </div>
                </div>

                {/* 密码分析 */}
                {analysis && (
                  <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                      <Clock className="w-6 h-6 mr-2 text-purple-600" />
                      {t('result.analysis')}
                    </h2>

                    {/* 强度指示器 */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('result.strength.label')}</span>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStrengthColor(analysis.strength)}`}>
                          {getStrengthText(analysis.strength)}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
                        <div 
                          className={`h-3 rounded-full transition-all duration-500 ${
                            analysis.strength === 'very-weak' ? 'bg-red-500' :
                            analysis.strength === 'weak' ? 'bg-orange-500' :
                            analysis.strength === 'fair' ? 'bg-yellow-500' :
                            analysis.strength === 'good' ? 'bg-blue-500' :
                            'bg-green-500'
                          }`}
                          style={{ width: `${analysis.score}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span>100%</span>
                      </div>
                    </div>

                    {/* 详细信息 */}
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="bg-gray-50 dark:bg-slate-700 rounded-xl p-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400">{t('result.score')}</div>
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">{analysis.score}/100</div>
                      </div>
                      <div className="bg-gray-50 dark:bg-slate-700 rounded-xl p-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400">{t('result.entropy')}</div>
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">{analysis.entropy.toFixed(1)} bits</div>
                      </div>
                    </div>

                    {/* 破解时间 */}
                    <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 rounded-xl p-4 mb-6">
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.crackTimeEstimate')}</div>
                      <div className="text-xl font-bold text-red-600 dark:text-red-400">{analysis.crackTime}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {t('result.crackTimeNote')}
                      </div>
                    </div>

                    {/* 建议 */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">{t('result.securityAdvice')}</h3>
                      <ul className="space-y-2">
                        {analysis.feedback.map((item, index) => (
                          <li key={index} className="flex items-start">
                            <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-sm text-gray-600 dark:text-gray-300">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 使用说明 */}
            <div className="mt-12 bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">{t('usage.title')}</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">{t('usage.security.title')}</h3>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                    {[0, 1, 2, 3, 4].map((index) => {
                      const item = t.raw(`usage.security.items.${index}`);
                      return item ? (
                        <li key={index}>• {item}</li>
                      ) : null;
                    }).filter(Boolean)}
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">{t('usage.features.title')}</h3>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                    {[0, 1, 2, 3, 4].map((index) => {
                      const item = t.raw(`usage.features.items.${index}`);
                      return item ? (
                        <li key={index}>• {item}</li>
                      ) : null;
                    }).filter(Boolean)}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div></div>
    </>
  );
}