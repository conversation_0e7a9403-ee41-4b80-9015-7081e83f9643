'use client'

import { useState, useCallback } from 'react'
import Link from 'next/link'
import { useLocale, useTranslations } from 'next-intl'
import { defaultLocale } from '@/i18n'
import { Shuffle, Copy, User, Sparkles } from 'lucide-react'
import { SITE_NAME, SITE_URL } from '@/lib/constants'

// 姓氏数据
const SURNAMES = {
  common: ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗'],
  ancient: ['司马', '欧阳', '上官', '东方', '独孤', '南宫', '西门', '慕容', '轩辕', '公孙', '令狐', '皇甫', '宇文', '长孙', '诸葛', '司徒', '司空', '夏侯', '完颜', '呼延'],
  rare: ['端木', '百里', '东郭', '南门', '西门', '北堂', '公羊', '万俟', '司寇', '仲孙', '钟离', '宗政', '濮阳', '淳于', '单于', '太叔', '申屠', '公冶', '宗正', '漆雕']
}

// 名字数据
const GIVEN_NAMES = {
  male: {
    modern: {
      single: ['浩', '轩', '宇', '博', '强', '明', '天', '杰', '文', '智', '豪', '然', '齐', '超', '君', '骞', '涛', '彬', '鹤', '越'],
      double: ['浩然', '子轩', '宇轩', '博文', '志强', '明轩', '天宇', '俊杰', '文博', '智宸', '正豪', '昊然', '绍齐', '博超', '君浩', '子骞', '鹏涛', '炎彬', '鹤轩', '越彬']
    },
    ancient: {
      single: ['墨', '云', '玉', '慕', '凌', '君', '忌', '逍', '傲', '绝', '飞', '寒', '破', '天', '极', '清', '明', '沧', '惊', '影'],
      double: ['墨轩', '云深', '玉琛', '慕白', '凌霄', '君临', '无忌', '逍遥', '傲天', '绝尘', '飞羽', '寒星', '破军', '天行', '无极', '清风', '明月', '沧海', '惊鸿', '绝影']
    },
    martial: {
      single: ['剑', '刀', '破', '斩', '御', '踏', '寻', '问', '听', '观', '望', '追', '凌', '浪', '惊', '骇', '雷', '电', '风', '烈'],
      double: ['剑心', '刀锋', '破天', '斩龙', '御风', '踏雪', '寻梅', '问剑', '听雨', '观澜', '望月', '追星', '凌云', '破浪', '惊涛', '骇浪', '雷鸣', '电闪', '风暴', '烈火']
    }
  },
  female: {
    modern: {
      single: ['雨', '梦', '雅', '韵', '莉', '璐', '玲', '妍', '玉', '嫣', '涵', '宸', '芙', '婷', '香', '瑶', '婉', '睿', '琳', '静'],
      double: ['雨萱', '梦琪', '雅静', '韵寒', '莉姿', '梦璐', '沛玲', '欣妍', '曼玉', '语嫣', '诗涵', '静宸', '雅芙', '雨婷', '怡香', '珺瑶', '梦瑶', '婉婷', '睿婕', '雅琳']
    },
    ancient: {
      single: ['若', '清', '浅', '流', '青', '紫', '碧', '素', '墨', '烟', '落', '寒', '幽', '芷', '婉', '画', '水', '倾', '绝', '无'],
      double: ['若汐', '清歌', '浅月', '流苏', '青鸾', '紫霞', '碧瑶', '素锦', '墨染', '烟雨', '落雪', '寒梅', '幽兰', '芷若', '婉清', '如画', '似水', '倾城', '绝代', '无双']
    },
    elegant: {
      single: ['诗', '书', '琴', '棋', '画', '舞', '歌', '花', '月', '云', '霓', '羽', '锦', '瑶', '凤', '玉', '银', '金', '珠', '绣'],
      double: ['诗雅', '书瑶', '琴心', '棋韵', '画意', '舞姿', '歌声', '花容', '月貌', '云裳', '霓裳', '羽衣', '锦瑟', '瑶琴', '凤箫', '玉笛', '银铃', '金钗', '珠帘', '绣帐']
    }
  }
}

// 生成风格配置
const STYLES = {
  modern: { name: '现代风格', description: '适合现代都市小说' },
  ancient: { name: '古风仙侠', description: '适合古装、仙侠、玄幻小说' },
  martial: { name: '武侠江湖', description: '适合武侠、江湖小说' },
  elegant: { name: '优雅古典', description: '适合古典言情小说' }
}

export default function NovelNameGeneratorPage() {
  const [gender, setGender] = useState('male')
  const [style, setStyle] = useState('modern')
  const [surnameType, setSurnameType] = useState('common')
  const [nameLength, setNameLength] = useState('random') // 'single', 'double', 'random'
  const [generatedNames, setGeneratedNames] = useState<string[]>([])

  const locale = useLocale()
  const isEnglish = locale === 'en'
  const [isGenerating, setIsGenerating] = useState(false)
  const t = useTranslations('pages.tools.tools.novelNameGenerator')

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // 这里可以添加提示
    } catch {
      console.error('复制失败')
    }
  }, [])

  // 生成随机姓名
  const generateName = useCallback(() => {
    const surnames = SURNAMES[surnameType as keyof typeof SURNAMES]
    const genderNames = gender === 'male' ? GIVEN_NAMES.male : GIVEN_NAMES.female
    const styleNames = genderNames[style as keyof typeof genderNames] || genderNames.modern
    
    const randomSurname = surnames[Math.floor(Math.random() * surnames.length)]
    
    // 根据nameLength选择名字类型
    let givenName: string
    if (nameLength === 'single') {
      const singleNames = styleNames.single
      givenName = singleNames[Math.floor(Math.random() * singleNames.length)]
    } else if (nameLength === 'double') {
      const doubleNames = styleNames.double
      givenName = doubleNames[Math.floor(Math.random() * doubleNames.length)]
    } else {
      // random: 随机选择单字名或双字名
      const useDouble = Math.random() > 0.5
      if (useDouble) {
        const doubleNames = styleNames.double
        givenName = doubleNames[Math.floor(Math.random() * doubleNames.length)]
      } else {
        const singleNames = styleNames.single
        givenName = singleNames[Math.floor(Math.random() * singleNames.length)]
      }
    }
    
    return randomSurname + givenName
  }, [gender, style, surnameType, nameLength])

  // 批量生成姓名
  const generateNames = useCallback(async () => {
    setIsGenerating(true)
    
    // 模拟生成延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const names = new Set<string>()
    while (names.size < 12) {
      names.add(generateName())
    }
    
    setGeneratedNames(Array.from(names))
    setIsGenerating(false)
  }, [generateName])

  // 预设示例
  const examples = [
    { gender: 'male', style: 'ancient', surname: 'ancient', description: '男性古风仙侠名字' },
    { gender: 'female', style: 'elegant', surname: 'common', description: '女性优雅古典名字' },
    { gender: 'male', style: 'martial', surname: 'rare', description: '男性武侠江湖名字' },
    { gender: 'female', style: 'modern', surname: 'common', description: '女性现代都市名字' }
  ]

  const handleExampleClick = (example: typeof examples[0]) => {
    setGender(example.gender)
    setStyle(example.style)
    setSurnameType(example.surname)
    // 自动生成一次
    setTimeout(() => {
      generateNames()
    }, 100)
  }

  // 如果是英文环境，显示说明页面
  if (isEnglish) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-rose-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              📚 Chinese Novel Name Generator
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Generate authentic Chinese names for novel characters
            </p>

            <div className="max-w-2xl mx-auto bg-pink-50 dark:bg-pink-900/20 border border-pink-200 dark:border-pink-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-pink-800 dark:text-pink-200 mb-4">
                🇨🇳 Chinese Literature Tool
              </h2>
              <p className="text-pink-700 dark:text-pink-300 mb-4">
                This tool generates authentic Chinese names specifically designed for novel characters.
                It includes various styles and categories suitable for different genres of Chinese literature.
              </p>
              <p className="text-pink-700 dark:text-pink-300 mb-4">
                <strong>Name Categories:</strong>
              </p>
              <ul className="text-pink-700 dark:text-pink-300 mb-6 text-left list-disc list-inside space-y-1">
                <li><strong>Modern Style:</strong> Contemporary Chinese names (现代风格)</li>
                <li><strong>Ancient Style:</strong> Classical names for historical/fantasy novels (古风仙侠)</li>
                <li><strong>Martial Arts:</strong> Names for wuxia/martial arts stories (武侠江湖)</li>
                <li><strong>Elegant Classical:</strong> Refined names for romance novels (优雅古典)</li>
              </ul>

              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded p-4 mb-6">
                <p className="text-purple-700 dark:text-purple-300 text-sm">
                  <strong>📖 Examples:</strong> 李浩然 (Li Haoran), 慕容雪 (Murong Xue), 独孤剑心 (Dugu Jianxin)
                </p>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-4 mb-6">
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  <strong>✨ Features:</strong> Gender selection, surname types (common/ancient/rare),
                  name length options, and style categories for different novel genres.
                </p>
              </div>

              <div className="flex justify-center">
                <Link
                  href="/tools/novel-name-generator"
                  className="inline-flex items-center px-6 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
                >
                  <Sparkles className="w-5 h-5 mr-2" />
                  Use Tool (Chinese Interface)
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "小说主角姓名生成工具",
            "description": "免费的在线小说主角姓名生成工具，支持现代、古风、武侠等多种风格的中文姓名生成。可自定义生成2个字或3个字姓名，适用于小说创作、游戏角色命名等场景。",
            "url": `${SITE_URL}/tools/novel-name-generator`,
            "applicationCategory": "CreativeApplication",
            "operatingSystem": "Any",
            "permissions": "browser",
            "isAccessibleForFree": true,
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "CNY"
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-20">
          {/* 面包屑导航 */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  首页
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href="/tools" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                    工具箱
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">小说姓名生成</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              ✨ 小说主角姓名生成工具
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              为你的小说主角生成富有特色的中文姓名，支持多种风格和类型，可自定义2个字或3个字姓名
            </p>
          </div>

          {/* 主要内容 */}
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-6">
              {/* 左侧：生成器配置 */}
              <div className="lg:col-span-2 space-y-6">
                {/* 配置选项 */}
                <div className="bg-white rounded-lg border shadow-sm">
                  <div className="p-6 pb-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <User className="h-5 w-5" />
                      角色设置
                    </h3>
                  </div>
                  <div className="px-6 pb-6 space-y-4">
                    {/* 性别选择 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">角色性别</label>
                      <div className="flex gap-3">
                        <button
                          onClick={() => setGender('male')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            gender === 'male'
                              ? 'bg-blue-50 border-blue-200 text-blue-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          👨 男性
                        </button>
                        <button
                          onClick={() => setGender('female')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            gender === 'female'
                              ? 'bg-pink-50 border-pink-200 text-pink-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          👩 女性
                        </button>
                      </div>
                    </div>

                    {/* 风格选择 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">姓名风格</label>
                      <div className="grid grid-cols-2 gap-3">
                        {Object.entries(STYLES).map(([key, styleInfo]) => (
                          <button
                            key={key}
                            onClick={() => setStyle(key)}
                            className={`p-3 rounded-lg border text-left transition-colors ${
                              style === key
                                ? 'bg-purple-50 border-purple-200 text-purple-700'
                                : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                            }`}
                          >
                            <div className="font-medium text-sm">{styleInfo.name}</div>
                            <div className="text-xs text-gray-600 mt-1">{styleInfo.description}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* 姓氏类型 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">姓氏类型</label>
                      <div className="flex gap-3">
                        <button
                          onClick={() => setSurnameType('common')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            surnameType === 'common'
                              ? 'bg-green-50 border-green-200 text-green-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          常见姓氏
                        </button>
                        <button
                          onClick={() => setSurnameType('ancient')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            surnameType === 'ancient'
                              ? 'bg-amber-50 border-amber-200 text-amber-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          复姓
                        </button>
                        <button
                          onClick={() => setSurnameType('rare')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            surnameType === 'rare'
                              ? 'bg-indigo-50 border-indigo-200 text-indigo-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          稀有姓氏
                        </button>
                      </div>
                    </div>

                    {/* 名字长度 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">名字长度</label>
                      <div className="flex gap-3">
                        <button
                          onClick={() => setNameLength('single')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            nameLength === 'single'
                              ? 'bg-blue-50 border-blue-200 text-blue-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          2个字
                        </button>
                        <button
                          onClick={() => setNameLength('double')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            nameLength === 'double'
                              ? 'bg-cyan-50 border-cyan-200 text-cyan-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          3个字
                        </button>
                        <button
                          onClick={() => setNameLength('random')}
                          className={`px-4 py-2 rounded-lg border transition-colors ${
                            nameLength === 'random'
                              ? 'bg-violet-50 border-violet-200 text-violet-700'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          随机长度
                        </button>
                      </div>
                    </div>

                    {/* 生成按钮 */}
                    <div className="pt-2">
                      <button
                        onClick={generateNames}
                        disabled={isGenerating}
                        className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                      >
                        {isGenerating ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                            生成中...
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4" />
                            生成姓名
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* 生成结果 */}
                {generatedNames.length > 0 && (
                  <div className="bg-white rounded-lg border shadow-sm">
                    <div className="p-6 pb-4">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <Shuffle className="h-5 w-5" />
                        生成结果
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        点击姓名可复制到剪贴板
                      </p>
                    </div>
                    <div className="px-6 pb-6">
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {generatedNames.map((name, index) => (
                          <div
                            key={index}
                            onClick={() => copyToClipboard(name)}
                            className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg cursor-pointer hover:from-purple-100 hover:to-pink-100 transition-colors group"
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-purple-800">{name}</span>
                              <Copy className="h-3 w-3 text-purple-600 opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 右侧：示例和说明 */}
              <div className="space-y-6">
                {/* 快速示例 */}
                <div className="bg-white rounded-lg border shadow-sm">
                  <div className="p-6 pb-4">
                    <h3 className="text-lg font-semibold">快速示例</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      点击快速设置并生成
                    </p>
                  </div>
                  <div className="px-6 pb-6">
                    <div className="space-y-3">
                      {examples.map((example, index) => (
                        <div
                          key={index}
                          className="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors"
                          onClick={() => handleExampleClick(example)}
                        >
                          <div className="font-medium text-sm text-purple-600 mb-1">
                            {example.description}
                          </div>
                          <div className="text-xs text-gray-600">
                            {example.gender === 'male' ? '👨' : '👩'} {STYLES[example.style as keyof typeof STYLES].name}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 使用说明 */}
                <div className="bg-white rounded-lg border shadow-sm">
                  <div className="p-6 pb-4">
                    <h3 className="text-lg font-semibold">使用说明</h3>
                  </div>
                  <div className="px-6 pb-6">
                    <div className="space-y-3 text-sm">
                      <div>
                        <div className="font-medium mb-1">风格特点：</div>
                        <div className="text-gray-600 space-y-1">
                          <div>• <strong>现代风格</strong>：适合都市、校园小说</div>
                          <div>• <strong>古风仙侠</strong>：适合玄幻、仙侠小说</div>
                          <div>• <strong>武侠江湖</strong>：适合武侠、江湖小说</div>
                          <div>• <strong>优雅古典</strong>：适合古典言情小说</div>
                        </div>
                      </div>
                      <div>
                        <div className="font-medium mb-1">姓氏类型：</div>
                        <div className="text-gray-600 space-y-1">
                          <div>• <strong>常见姓氏</strong>：李、王、张等常用姓氏</div>
                          <div>• <strong>复姓</strong>：司马、欧阳、上官等</div>
                          <div>• <strong>稀有姓氏</strong>：端木、百里等罕见姓氏</div>
                        </div>
                      </div>
                      <div>
                        <div className="font-medium mb-1">名字长度：</div>
                        <div className="text-gray-600 space-y-1">
                          <div>• <strong>2个字</strong>：姓氏+单字名（如：李浩）</div>
                          <div>• <strong>3个字</strong>：姓氏+双字名（如：李浩然）</div>
                          <div>• <strong>随机长度</strong>：随机生成2字或3字姓名</div>
                        </div>
                      </div>
                      <div>
                        <div className="font-medium mb-1">应用场景：</div>
                        <div className="text-gray-600 space-y-1">
                          <div>• 小说创作角色命名</div>
                          <div>• 游戏角色取名</div>
                          <div>• 剧本人物设定</div>
                          <div>• 创意写作灵感</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}