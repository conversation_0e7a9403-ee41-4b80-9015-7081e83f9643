import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from '@/lib/constants'

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.novelNameGenerator' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const getHreflangAlternates = (basePath: string) => {
    return {
      'zh': basePath,
      'en': `/en${basePath}`,
    };
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/novel-name-generator');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      '小说姓名生成器',
      '主角姓名生成',
      '中文姓名生成',
      '2个字姓名生成',
      '3个字姓名生成',
      '姓名长度选择',
      '古风姓名',
      '武侠姓名',
      '现代姓名',
      '小说创作工具',
      '角色命名',
      '游戏角色取名',
      '复姓生成',
      '稀有姓氏',
      '创意写作',
      '剧本人物',
      '免费姓名生成器',
      '在线工具'
    ] : [
      'novel name generator',
      'character name generator',
      'Chinese name generator',
      '2-character name generation',
      '3-character name generation',
      'name length selection',
      'ancient style names',
      'martial arts names',
      'modern names',
      'novel writing tools',
      'character naming',
      'game character naming',
      'compound surname generation',
      'rare surnames',
      'creative writing',
      'script characters',
      'free name generator',
      'online tools'
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: getHreflangAlternates('/tools/novel-name-generator'),
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
      images: [
        {
          url: '/images/tools/novel-name-generator-og.jpg',
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: ['/images/tools/novel-name-generator-og.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default function NovelNameGeneratorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}