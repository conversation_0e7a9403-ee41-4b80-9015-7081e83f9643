import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const title = `${t('title')} | ${tSite('name')}`;
  const description = t('description');
  const canonicalPath = getLocalizedPath('/tools');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "在线工具",
      "开发工具",
      "实用工具",
      "免费工具",
      "网页工具",
      "编程工具",
      "转换工具",
      "格式化工具",
      "生成器工具",
      "验证工具"
    ] : [
      "online tools",
      "developer tools",
      "utility tools",
      "free tools",
      "web tools",
      "programming tools",
      "converter tools",
      "formatter tools",
      "generator tools",
      "validator tools"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: {
        'zh': '/tools',
        'en': '/en/tools',
      },
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools-og.jpg",
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function ToolsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
