import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.qrGenerator' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const getHreflangAlternates = (basePath: string) => {
    return {
      'zh': basePath,
      'en': `/en${basePath}`,
    };
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/qr-generator');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "二维码生成器",
      "QR码生成",
      "在线二维码制作",
      "免费二维码工具",
      "二维码生成",
      "QR Code Generator",
      "二维码制作",
      "条形码生成",
      "二维码下载",
      "自定义二维码",
      "WiFi二维码",
      "名片二维码"
    ] : [
      "QR code generator",
      "QR code creation",
      "online QR maker",
      "free QR tools",
      "QR code generation",
      "QR Code Generator",
      "QR code maker",
      "barcode generator",
      "QR code download",
      "custom QR code",
      "WiFi QR code",
      "business card QR"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: getHreflangAlternates('/tools/qr-generator'),
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools/qr-generator-og.jpg",
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools/qr-generator-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function QRGeneratorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}