import QRCode from 'qrcode';

// 生成二维码的工具函数
export const generateQRCode = async (
  text: string, 
  size: number, 
  errorLevel: 'L' | 'M' | 'Q' | 'H', 
  foregroundColor: string, 
  backgroundColor: string
): Promise<string> => {
  try {
    const options = {
      errorCorrectionLevel: errorLevel,
      type: 'image/png' as const,
      quality: 0.92,
      margin: 1,
      color: {
        dark: foregroundColor,
        light: backgroundColor,
      },
      width: size,
    };

    return await QRCode.toDataURL(text, options);
  } catch (error) {
    console.error('QR码生成失败:', error);
    return '';
  }
};

// 生成SVG格式二维码
export const generateSVGQRCode = async (
  text: string, 
  size: number, 
  errorLevel: 'L' | 'M' | 'Q' | 'H', 
  foregroundColor: string, 
  backgroundColor: string
): Promise<string> => {
  try {
    const options = {
      errorCorrectionLevel: errorLevel,
      type: 'svg' as const,
      margin: 1,
      color: {
        dark: foregroundColor,
        light: backgroundColor,
      },
      width: size,
    };

    return await QRCode.toString(text, options);
  } catch (error) {
    console.error('SVG QR码生成失败:', error);
    return '';
  }
};

// 生成内容的工具函数
export const generateContentFromData = (
  qrType: string,
  textContent: string,
  urlContent: string,
  wifiConfig: {
    ssid: string;
    password: string;
    security: string;
    hidden: boolean;
  },
  vcardInfo: {
    name: string;
    phone: string;
    email: string;
    company: string;
    title: string;
    website: string;
  }
): string => {
  switch (qrType) {
    case 'text':
      return textContent;
    case 'url':
      return urlContent;
    case 'wifi':
      return `WIFI:T:${wifiConfig.security};S:${wifiConfig.ssid};P:${wifiConfig.password};H:${wifiConfig.hidden ? 'true' : 'false'};;`;
    case 'vcard':
      return `BEGIN:VCARD
VERSION:3.0
FN:${vcardInfo.name}
TEL:${vcardInfo.phone}
EMAIL:${vcardInfo.email}
ORG:${vcardInfo.company}
TITLE:${vcardInfo.title}
URL:${vcardInfo.website}
END:VCARD`;
    default:
      return '';
  }
};