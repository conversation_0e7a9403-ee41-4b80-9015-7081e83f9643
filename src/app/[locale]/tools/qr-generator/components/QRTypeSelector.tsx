import { useTranslations } from 'next-intl';

interface QRTypeSelectorProps {
  qrType: string;
  setQrType: (type: string) => void;
}

export default function QRTypeSelector({ qrType, setQrType }: QRTypeSelectorProps) {
  const t = useTranslations('pages.tools.tools.qrGenerator');

  const qrTypes = [
    { value: 'text', label: `📝 ${t('types.text')}`, desc: t('content.text') },
    { value: 'url', label: `🔗 ${t('types.url')}`, desc: t('content.url') },
    { value: 'wifi', label: `📶 ${t('types.wifi')}`, desc: 'WiFi' },
    { value: 'vcard', label: `👤 ${t('types.vcard')}`, desc: t('types.vcard') }
  ];

  return (
    <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
      <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
        {t('types.title')}
      </h2>
      <div className="grid grid-cols-2 gap-3">
        {qrTypes.map((type) => (
          <button
            key={type.value}
            onClick={() => setQrType(type.value)}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              qrType === type.value
                ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                : 'border-gray-200 dark:border-slate-600 hover:border-purple-300'
            }`}
          >
            <div className="font-medium text-gray-900 dark:text-white">
              {type.label}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {type.desc}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}