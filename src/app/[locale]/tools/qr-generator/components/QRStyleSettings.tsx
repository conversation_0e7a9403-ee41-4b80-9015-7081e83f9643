import { useTranslations } from 'next-intl';

interface QRStyleSettingsProps {
  qrSize: number;
  setQrSize: (size: number) => void;
  errorLevel: string;
  setErrorLevel: (level: string) => void;
  foregroundColor: string;
  setForegroundColor: (color: string) => void;
  backgroundColor: string;
  setBackgroundColor: (color: string) => void;
  showAdvanced: boolean;
  setShowAdvanced: (show: boolean) => void;
}

export default function QRStyleSettings({
  qrSize,
  setQrSize,
  errorLevel,
  setErrorLevel,
  foregroundColor,
  setForegroundColor,
  backgroundColor,
  setBackgroundColor,
  showAdvanced,
  setShowAdvanced
}: QRStyleSettingsProps) {
  const t = useTranslations('pages.tools.tools.qrGenerator');


  return (
    <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {t('style.title')}
        </h2>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium"
        >
          {showAdvanced ? t('style.collapseAdvanced') : t('style.expandAdvanced')}
        </button>
      </div>

      <div className="space-y-4">
        {/* 基础设置 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('style.size')}: {qrSize}px
          </label>
          <input
            type="range"
            min="200"
            max="800"
            step="50"
            value={qrSize}
            onChange={(e) => setQrSize(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>200px</span>
            <span>800px</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('style.errorLevel')}
          </label>
          <select
            value={errorLevel}
            onChange={(e) => setErrorLevel(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
          >
            <option value="L">{t('style.errorLevels.L')}</option>
            <option value="M">{t('style.errorLevels.M')}</option>
            <option value="Q">{t('style.errorLevels.Q')}</option>
            <option value="H">{t('style.errorLevels.H')}</option>
          </select>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {t('style.errorLevelTip')}
          </p>
        </div>

        {/* 高级设置 */}
        {showAdvanced && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('style.foreground')}
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={foregroundColor}
                  onChange={(e) => setForegroundColor(e.target.value)}
                  className="w-12 h-10 border border-gray-300 dark:border-slate-600 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={foregroundColor}
                  onChange={(e) => setForegroundColor(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white font-mono text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('style.background')}
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="w-12 h-10 border border-gray-300 dark:border-slate-600 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white font-mono text-sm"
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}