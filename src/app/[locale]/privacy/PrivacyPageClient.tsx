'use client';

type TranslationsType = {
  title: string;
  lastUpdated: string;
  sections: {
    informationCollection: {
      title: string;
      description: string;
      items: {
        email: string;
        access: string;
        usage: string;
      };
    };
    informationUsage: {
      title: string;
      description: string;
      items: string[];
    };
    informationProtection: {
      title: string;
      description: string;
      items: string[];
    };
    cookies: {
      title: string;
      description: string;
      items: string[];
      control: string;
    };
    userRights: {
      title: string;
      description: string;
      items: string[];
    };
    advertising: {
      title: string;
      description: string;
      items: string[];
      important: string;
      importantItems: string[];
    };
    thirdParty: {
      title: string;
      description: string;
      items: string[];
      recommendation: string;
    };
    policyUpdates: {
      title: string;
      description: string;
    };
    contact: {
      title: string;
      description: string;
      items: {
        email: string;
        website: string;
      };
    };
  };
  summary: string;
  siteName: string;
};

type Props = {
  locale: string;
  translations: TranslationsType;
};

export default function PrivacyPageClient({ locale, translations: t }: Props) {
  return (
    <main>
      <div className="container-custom">
        <div className="max-w-4xl mx-auto py-16">
          <div className="prose prose-lg dark:prose-invert max-w-none">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-8">
              {t.title}
            </h1>
            
            <div className="text-sm text-slate-600 dark:text-slate-400 mb-8">
              {t.lastUpdated}：{new Date().toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
            </div>

            <div className="space-y-8">
              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.informationCollection.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.informationCollection.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li><strong>{t.sections.informationCollection.items.email}</strong></li>
                    <li><strong>{t.sections.informationCollection.items.access}</strong></li>
                    <li><strong>{t.sections.informationCollection.items.usage}</strong></li>
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.informationUsage.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.informationUsage.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.informationUsage.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.informationProtection.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.informationProtection.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.informationProtection.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.cookies.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.cookies.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.cookies.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <p>{t.sections.cookies.control}</p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.userRights.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.userRights.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.userRights.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.advertising.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.advertising.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.advertising.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <p><strong>{t.sections.advertising.important}</strong></p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.advertising.importantItems.map((item: string, index: number) => (
                      <li key={index}>
                        {index === 1 ? (
                          <>
                            {item.split('Google广告设置')[0]}
                            <a href="https://www.google.com/settings/ads" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">
                              {locale === 'zh' ? 'Google广告设置' : 'Google Ad Settings'}
                            </a>
                            {item.split('Google广告设置')[1] || item.split('Google Ad Settings')[1]}
                          </>
                        ) : index === 2 ? (
                          <>
                            {item.split('aboutads.info')[0]}
                            <a href="http://www.aboutads.info/choices/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">
                              aboutads.info
                            </a>
                            {item.split('aboutads.info')[1]}
                          </>
                        ) : (
                          item
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.thirdParty.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.thirdParty.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.thirdParty.items.map((item: string, index: number) => (
                      <li key={index}>
                        {index === 0 || index === 1 ? (
                          <>
                            <strong>{item.split('：')[0]}：</strong>
                            {item.split('：')[1]?.split('（')[0]}
                            （<a href="https://policies.google.com/privacy" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">
                              {locale === 'zh' ? '隐私政策' : 'Privacy Policy'}
                            </a>）
                          </>
                        ) : (
                          item
                        )}
                      </li>
                    ))}
                  </ul>
                  <p>{t.sections.thirdParty.recommendation}</p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.policyUpdates.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.policyUpdates.description}</p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.contact.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.contact.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>{t.sections.contact.items.email}</li>
                    <li>
                      {t.sections.contact.items.website}
                      
                    </li>
                  </ul>
                </div>
              </section>
            </div>

            <div className="mt-12 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>{locale === 'zh' ? '简而言之：' : 'In short:'}</strong>{t.summary}
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}