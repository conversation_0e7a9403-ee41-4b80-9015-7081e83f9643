
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import TermsPageClient from './TermsPageClient';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.terms' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  return {
    title: `${t('title')} | ${tSite('name')}`,
    description: t('description'),
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: locale === 'en' ? '/en/terms' : '/terms',
      languages: {
        'zh': '/terms',
        'en': '/en/terms',
      },
    },
    openGraph: {
      title: `${t('title')} | ${tSite('name')}`,
      description: t('description'),
      url: locale === 'en' ? '/en/terms' : '/terms',
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function TermsPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.terms' });

  // Pre-fetch all translation data
  const translations = {
    title: t('title'),
    description: t('description'),
    lastUpdated: t('lastUpdated'),
    sections: {
      acceptance: {
        title: t('sections.acceptance.title'),
        description: t('sections.acceptance.description', { siteName: SITE_NAME })
      },
      copyright: {
        title: t('sections.copyright.title'),
        description: t('sections.copyright.description'),
        items: [
          t('sections.copyright.items.0'),
          t('sections.copyright.items.1'),
          t('sections.copyright.items.2'),
          t('sections.copyright.items.3')
        ],
        protection: t('sections.copyright.protection', { siteName: SITE_NAME })
      },
      fairUse: {
        title: t('sections.fairUse.title'),
        allowedTitle: t('sections.fairUse.allowedTitle'),
        allowed: [
          t('sections.fairUse.allowed.0'),
          t('sections.fairUse.allowed.1'),
          t('sections.fairUse.allowed.2')
        ],
        prohibitedTitle: t('sections.fairUse.prohibitedTitle'),
        prohibited: [
          t('sections.fairUse.prohibited.0'),
          t('sections.fairUse.prohibited.1'),
          t('sections.fairUse.prohibited.2'),
          t('sections.fairUse.prohibited.3')
        ]
      },
      disclaimer: {
        title: t('sections.disclaimer.title'),
        description: t('sections.disclaimer.description'),
        items: [
          t('sections.disclaimer.items.0'),
          t('sections.disclaimer.items.1'),
          t('sections.disclaimer.items.2'),
          t('sections.disclaimer.items.3')
        ],
        warning: t('sections.disclaimer.warning')
      },
      conduct: {
        title: t('sections.conduct.title'),
        description: t('sections.conduct.description'),
        items: [
          t('sections.conduct.items.0'),
          t('sections.conduct.items.1'),
          t('sections.conduct.items.2'),
          t('sections.conduct.items.3'),
          t('sections.conduct.items.4')
        ]
      },
      thirdPartyLinks: {
        title: t('sections.thirdPartyLinks.title'),
        description: t('sections.thirdPartyLinks.description')
      },
      availability: {
        title: t('sections.availability.title'),
        description: t('sections.availability.description'),
        items: [
          t('sections.availability.items.0'),
          t('sections.availability.items.1'),
          t('sections.availability.items.2')
        ],
        rights: t('sections.availability.rights')
      },
      changes: {
        title: t('sections.changes.title'),
        description: t('sections.changes.description')
      },
      contact: {
        title: t('sections.contact.title'),
        description: t('sections.contact.description'),
        items: {
          email: t('sections.contact.items.email'),
          website: t('sections.contact.items.website')
        }
      }
    },
    summary: t('summary')
  };

  return <TermsPageClient locale={locale} translations={translations} />;
}