'use client';

import { SITE_NAME } from "@/lib/constants";

type TranslationsType = {
  title: string;
  description: string;
  lastUpdated: string;
  sections: {
    acceptance: {
      title: string;
      description: string;
    };
    copyright: {
      title: string;
      description: string;
      items: string[];
      protection: string;
    };
    fairUse: {
      title: string;
      allowedTitle: string;
      allowed: string[];
      prohibitedTitle: string;
      prohibited: string[];
    };
    disclaimer: {
      title: string;
      description: string;
      items: string[];
      warning: string;
    };
    conduct: {
      title: string;
      description: string;
      items: string[];
    };
    thirdPartyLinks: {
      title: string;
      description: string;
    };
    availability: {
      title: string;
      description: string;
      items: string[];
      rights: string;
    };
    changes: {
      title: string;
      description: string;
    };
    contact: {
      title: string;
      description: string;
      items: {
        email: string;
        website: string;
      };
    };
  };
  summary: string;
};

type Props = {
  locale: string;
  translations: TranslationsType;
};

export default function TermsPageClient({ locale, translations: t }: Props) {
  const formatDate = (locale: string) => {
    const date = new Date();
    return locale === 'zh' ? date.toLocaleDateString('zh-CN') : date.toLocaleDateString('en-US');
  };

  const formatSiteName = (text: string) => {
    return text.replace('{siteName}', SITE_NAME);
  };

  return (
    <main>
      <div className="container-custom">
        <div className="max-w-4xl mx-auto py-16">
          <div className="prose prose-lg dark:prose-invert max-w-none">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-8">
              {t.title}
            </h1>
            
            <div className="text-sm text-slate-600 dark:text-slate-400 mb-8">
              {t.lastUpdated}：{formatDate(locale)}
            </div>

            <div className="space-y-8">
              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.acceptance.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {formatSiteName(t.sections.acceptance.description)}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.copyright.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.sections.copyright.description}
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.copyright.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <p>
                    {formatSiteName(t.sections.copyright.protection)}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.fairUse.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.fairUse.allowedTitle}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.fairUse.allowed.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <p>{t.sections.fairUse.prohibitedTitle}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.fairUse.prohibited.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.disclaimer.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.sections.disclaimer.description}
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.disclaimer.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <p className="font-semibold">
                    {t.sections.disclaimer.warning}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.conduct.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.conduct.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.conduct.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.thirdPartyLinks.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.sections.thirdPartyLinks.description}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.availability.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.sections.availability.description}
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.availability.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <p>
                    {t.sections.availability.rights}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.changes.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.sections.changes.description}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.contact.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.sections.contact.description}
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>{t.sections.contact.items.email}</li>
                    <li>{t.sections.contact.items.website}</li>
                  </ul>
                </div>
              </section>
            </div>

            <div className="mt-12 p-6 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <p className="text-sm text-green-800 dark:text-green-200">
                <strong>{locale === 'zh' ? '简而言之：' : 'In short:'}</strong>{t.summary}
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
