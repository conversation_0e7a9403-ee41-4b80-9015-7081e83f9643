
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import AdsPolicyPageClient from './AdsPolicyPageClient';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.adsPolicy' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  return {
    title: `${t('title')} - ${tSite('name')}`,
    description: t('description'),
    keywords: locale === 'zh' ? 
      ["广告政策", "Google AdSense", "个性化广告", "广告管理", "隐私设置"] :
      ["advertising policy", "Google AdSense", "personalized ads", "ad management", "privacy settings"],
    authors: [{ name: tSite('name') }],
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: locale === 'en' ? '/en/ads-policy' : '/ads-policy',
      languages: {
        'zh': '/ads-policy',
        'en': '/en/ads-policy',
      },
    },
    openGraph: {
      type: "website",
      locale: locale === 'zh' ? "zh_CN" : "en_US",
      title: `${t('title')} - ${tSite('name')}`,
      description: t('description'),
      siteName: tSite('name'),
      url: locale === 'en' ? '/en/ads-policy' : '/ads-policy',
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function AdsPolicyPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.adsPolicy' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  // Pre-fetch all translation data
  const translations = {
    title: t('title'),
    lastUpdated: t('lastUpdated'),
    sections: {
      adDisplay: {
        title: t('sections.adDisplay.title'),
        description: t('sections.adDisplay.description')
      },
      googleAdsense: {
        title: t('sections.googleAdsense.title'),
        description: t('sections.googleAdsense.description'),
        items: [
          t('sections.googleAdsense.items.0'),
          t('sections.googleAdsense.items.1'),
          t('sections.googleAdsense.items.2'),
          t('sections.googleAdsense.items.3')
        ]
      },
      personalizedAds: {
        title: t('sections.personalizedAds.title'),
        description: t('sections.personalizedAds.description'),
        items: [
          t('sections.personalizedAds.items.0'),
          t('sections.personalizedAds.items.1'),
          t('sections.personalizedAds.items.2'),
          t('sections.personalizedAds.items.3'),
          t('sections.personalizedAds.items.4')
        ]
      },
      adPreferences: {
        title: t('sections.adPreferences.title'),
        description: t('sections.adPreferences.description'),
        items: [
          t('sections.adPreferences.items.0'),
          t('sections.adPreferences.items.1'),
          t('sections.adPreferences.items.2'),
          t('sections.adPreferences.items.3')
        ]
      },
      thirdPartyNetworks: {
        title: t('sections.thirdPartyNetworks.title'),
        description: t('sections.thirdPartyNetworks.description'),
        items: [
          t('sections.thirdPartyNetworks.items.0'),
          t('sections.thirdPartyNetworks.items.1'),
          t('sections.thirdPartyNetworks.items.2')
        ]
      },
      adContentResponsibility: {
        title: t('sections.adContentResponsibility.title'),
        description: t('sections.adContentResponsibility.description'),
        items: [
          t('sections.adContentResponsibility.items.0'),
          t('sections.adContentResponsibility.items.1'),
          t('sections.adContentResponsibility.items.2'),
          t('sections.adContentResponsibility.items.3')
        ]
      },
      childrenPrivacy: {
        title: t('sections.childrenPrivacy.title'),
        description: t('sections.childrenPrivacy.description'),
        items: [
          t('sections.childrenPrivacy.items.0'),
          t('sections.childrenPrivacy.items.1'),
          t('sections.childrenPrivacy.items.2')
        ]
      },
      contact: {
        title: t('sections.contact.title'),
        description: t('sections.contact.description'),
        items: {
          email: t('sections.contact.items.email'),
          website: t('sections.contact.items.website')
        }
      }
    },
    reminder: t('reminder'),
    siteName: tSite('name')
  };

  return <AdsPolicyPageClient locale={locale} translations={translations} />;
}
