"use client";

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { pageview, isGAEnabled } from '@/lib/gtag';

export function usePageTracking() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (!isGAEnabled) return;

    // 构建完整的 URL
    const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    
    // 发送页面浏览事件
    pageview(url);
    
    // 调试信息（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('GA Pageview:', url);
    }
  }, [pathname, searchParams]);
}

// 用于手动跟踪页面浏览的函数
export function trackPageView(url?: string) {
  if (!isGAEnabled || typeof window === 'undefined') return;
  
  const trackingUrl = url || window.location.pathname + window.location.search;
  pageview(trackingUrl);
}
