---
excerpt: "Built 5 products, 3 failed completely, 2 barely surviving. Sharing the mistakes I made in product idea discovery and validation, plus some practical methods I learned."
coverImage: "/assets/blog/7.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
featured: false
title: "After Building 5 Products, I Finally Understand: How Good Ideas Really Come"
date: "2025-06-20"
lastModified: "2025-06-20"
---

Over the past 3 years, I built 5 products. 3 failed completely, 2 are barely surviving, and none truly succeeded.

Looking back, the biggest problem wasn't poor technical skills or lack of execution, but the ideas themselves were flawed. Either they were fake needs, the market was too small, or I was just wishfully thinking others needed them.

Today I want to share the mistakes I made in product idea discovery and validation, plus some methods I've learned. Hope this helps others avoid some detours.

## Mistakes I Made

### First Mistake: Thinking "Useful to Me" Means "Useful to Everyone"

My first product was a code snippet management tool. As a programmer, I often needed to save commonly used code snippets and felt existing tools weren't good enough, so I built my own.

Spent 2 months developing it with comprehensive features and a nice interface. After launch, except for a few friends who registered out of courtesy, basically no one used it.

Later I realized that while I had this need, most programmers were satisfied with their IDE's snippet features or just used GitHub Gist. My tool had more features but also higher learning costs - people had no motivation to switch.

### Second Mistake: Ideas Too Big, Trying to Do Everything

My second product was an "all-in-one" project management tool, trying to integrate task management, time tracking, team collaboration, document management, etc.

I thought existing tools all had various problems, so I'd create a perfect solution. Result: after 6 months of development, features became increasingly complex, and I couldn't even figure out what the focus was.

What I ended up with was a jack-of-all-trades - every feature existed but none were good enough. User feedback after trials was "too many features, don't know where to start."

### Third Mistake: Starting Development Without Validation

My third product was a tool to help content creators manage multi-platform publishing. I saw many people complaining about the hassle of publishing content across WeChat, Zhihu, Xiaohongshu, etc., and thought this was a good opportunity.

Without any validation, I started developing directly. After 3 months of work, I discovered most content creators actually don't need such tools because different platforms have very different content strategies and format requirements - few people publish identical content across platforms.

## How I Find Ideas Now

After these failures, I've summarized some relatively reliable methods:

### Start from Your Own Real Pain Points

This method itself isn't wrong, but pay attention to a few things:

**Ensure the pain point is painful enough**: Not every inconvenience is worth turning into a product. I now ask myself: How much does this problem pain me? How much would I pay to solve it?

**Ensure you're not the only one with this pain point**: Before developing, I first ask in relevant groups and forums to see if others encounter similar problems.

**Start small**: Don't try to solve all problems at once - focus on one core pain point first.

### Observe Others' Complaints

I now frequently browse vertical communities like V2EX, Jike, Xiaohongshu, specifically looking for people's complaints.

For example, I often see designers complaining about the hassle of finding materials, programmers complaining about complex deployment processes, and marketers complaining about difficult data analysis tools.

Behind these complaints often lie real needs.

### Focus on Niche Markets

Don't try to create a universal solution - focus on specific needs of specific groups.

For example, instead of making a general project management tool, make one specifically for design teams; instead of a general accounting app, make financial management software specifically for freelancers.

Niche markets may have smaller user bases, but needs are clearer and competition is relatively less.

## How I Validate Ideas Now

After having an idea, I don't immediately start developing - I validate first.

### Chat with Potential Users

I find 10-20 people who match my target user profile and have in-depth conversations with them.

The focus isn't asking "what do you think of my idea" but understanding:
- How do they currently solve this problem?
- What are they unsatisfied with in existing solutions?
- How much would they pay for a better solution?
- If such a product existed, would they use it immediately?

### Create a Simple Landing Page

Before developing the product, I first create a landing page clearly describing the product's value and features, then run small ad campaigns to see how many people are willing to leave their email to express interest.

If even the landing page doesn't interest anyone, the actual product definitely won't have users.

### Manually Provide Services

For products that can be manually operated, I first provide services manually to a few users to validate the authenticity of the need.

For example, for a data analysis tool I'm currently working on, before developing automation features, I manually help several clients with data analysis to validate needs and optimize processes through this experience.

## Some Judgment Criteria

Through these experiences, I've summarized several criteria for judging whether an idea is reliable:

**Users willing to pay**: If users won't even pay 10 yuan, the need isn't strong enough.

**Users willing to spend time**: If users won't spend half an hour discussing needs with you, the problem isn't important enough to them.

**Clear target users**: If you can't clearly say who your product is for, it's basically a fake need.

**Solves frequently occurring problems**: If users only encounter this problem once a year, market space is very limited.

## Final Thoughts

The biggest fear in product development is working behind closed doors. My previous failures were largely because I trusted my own judgment too much without sufficiently validating market demand.

My principle now is: before writing the first line of code, ensure at least 10 people are willing to pay for this product. While this doesn't guarantee success, it at least avoids building something completely unwanted.

Hope my experiences help everyone. If you have similar experiences, welcome to share and exchange!
