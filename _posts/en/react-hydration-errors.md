---
featured: false
title: "React Hydration Errors Drove Me Crazy, Finally Found the Solution"
excerpt: "Encountered Hydration errors while working on a Next.js project, took me two days to figure out. Sharing the pitfalls I encountered and solutions, hoping to help others avoid detours."
coverImage: "/assets/blog/16.png"
date: "2025-07-01"
lastModified: "2025-07-01"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
---

Last month while working on a Next.js project, I encountered a problem that gave me headaches for two days: Hydration errors.

The console kept showing `Text content does not match server-rendered HTML`, the page looked normal, but this annoying error persisted. Googled for ages, tried various methods, finally figured it out.

Today I'm sharing my debugging experience and solutions, hoping to help friends who encounter the same issues.

## What is Hydration?

Honestly, I was pretty confused about this concept at first. Simply put:

1. **Server renders first**: Next.js generates HTML on the server, sends it to browser
2. **<PERSON><PERSON>er displays static page**: Users can see content, but can't interact yet
3. **<PERSON>act takes over**: After JavaScript loads, <PERSON>act "activates" these static HTML elements, making them interactive components

This "activation" process is called Hydration.

## Problems I Encountered

### Problem 1: Device Type Detection

I wanted to display different content based on screen width:

```jsx
function MyComponent() {
  // This caused problems
  const isMobile = window.innerWidth < 768;
  return <div>{isMobile ? "Mobile" : "Desktop"}</div>;
}
```

Console went crazy with errors. The reason is there's no `window` object during server rendering, so server and client render different results.

### Problem 2: Displaying Current Time

I wanted to show current time on the page:

```jsx
function TimeDisplay() {
  const now = new Date().toLocaleTimeString();
  return <span>Current time: {now}</span>;
}
```

Error again! Because server rendering time and client rendering time are definitely different.

### Problem 3: Random IDs

I used random numbers to generate component IDs:

```jsx
function RandomComponent() {
  const id = Math.random().toString(36);
  return <div id={id}>Random component</div>;
}
```

Same problem - server and client can't possibly generate the same random numbers.

## My Solutions

After various attempts, I summarized several effective methods:

### Method 1: Delayed Rendering

This is my most-used method, wait until component mounts before rendering client-dependent content:

```jsx
import { useState, useEffect } from "react";

function MyComponent() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Show placeholder during server rendering and client's first render
    return <div>Loading...</div>;
  }

  // Only render real content after client mounting
  const isMobile = window.innerWidth < 768;
  return <div>{isMobile ? "Mobile" : "Desktop"}</div>;
}
```

### Method 2: Dynamic Import

If entire component only needs client-side rendering, use Next.js dynamic import:

```jsx
import dynamic from "next/dynamic";

const ClientOnlyComponent = dynamic(() => import("../components/MyClientComponent"), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});

function MyPage() {
  return (
    <div>
      <h1>Page Title</h1>
      <ClientOnlyComponent />
    </div>
  );
}
```

### Method 3: Custom Hook

I wrote a general Hook to handle this situation:

```jsx
import { useState, useEffect } from "react";

function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

// Usage
function MyComponent() {
  const isClient = useIsClient();

  if (!isClient) {
    return <div>Loading...</div>;
  }

  return <div>{window.innerWidth < 768 ? "Mobile" : "Desktop"}</div>;
}
```

### Method 4: Suppress Warnings (Use Carefully)

For trivial differences like time display, you can use `suppressHydrationWarning`:

```jsx
function TimeDisplay() {
  return <span suppressHydrationWarning>Current time: {new Date().toLocaleTimeString()}</span>;
}
```

But this only hides the warning without solving the root problem, I generally don't recommend it.

## Summary

Although Hydration errors are annoying, they're not hard to solve once you understand the principles. Core idea is: **Ensure server and client first render results are consistent**.

For features that must depend on client environment, delay them until after component mounting. Though there might be slight flickering, it's much better than errors.

Hope my experience helps everyone. If you've encountered similar problems, welcome to share your solutions!
