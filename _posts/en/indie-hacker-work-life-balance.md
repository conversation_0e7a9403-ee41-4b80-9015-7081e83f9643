---
excerpt: "Been doing indie development for 3 years, from being a 24-hour workaholic to finding relative balance. Sharing some mistakes I made and methods I found. Not motivational fluff, all hard-learned lessons."
coverImage: "/assets/blog/6.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
featured: false
title: "3 Years of Indie Development: How <PERSON> Went from Workaholic to Normal Person"
date: "2025-06-10"
lastModified: "2025-06-10"
---

Three years ago I quit my job to start indie development, thinking I was finally free: no clocking in, no boring meetings, work whenever I wanted.

What happened? I became a 24-hour workaholic.

First thing in the morning was checking data, last thing before bed was still thinking about code. Weekends? What weekends? Holidays? Those were for other people. I told myself it was for my dreams, for freedom, but actually I was more tired and anxious than when I had a regular job.

It wasn't until last year, when my health suffered from chronic sleep deprivation and stress, that I started reflecting on this lifestyle. After over a year of adjustments, I've finally found some balance.

Today I'm sharing some of my experiences, hoping to help fellow indie developers who are struggling with similar issues.

## Mindset Adjustment: Don't Drive Yourself to Death

### Perfectionism is Poison

I used to be a severe perfectionist. I'd revise a feature dozens of times, adjust an interface countless times, always feeling it wasn't good enough. Result? Products never launched, missing optimal timing.

Later I forced myself to accept the "good enough" philosophy. An 80-point product that's live is much better than a 100-point product that's forever in development. User feedback will tell you what really needs optimization.

### Lower Expectations, Celebrate Small Progress

When I first started indie development, I always fantasized about getting rich overnight. Seeing stories of others earning tens of thousands monthly, I thought I could do it quickly too. Result: higher expectations, bigger disappointments.

Now I've learned to set small goals: complete one feature this week, get 5 users this month, earn 1000 yuan this quarter. Every time I achieve a small goal, I give myself a little reward, like buying something I like or going out for a nice meal.

### Find Your Tribe, Don't Carry Everything Alone

Indie development is really lonely. No colleagues to chat with, no boss to give direction, all decisions are yours to make.

I joined several indie developer WeChat groups and regularly attend offline meetups. Having these friends, I can discuss problems when they arise and share progress when I make it. The psychological pressure is much less.

I especially recommend finding one or two friends for deep exchanges, regularly chatting about each other's project progress and concerns. This support is really important.

## Time Management: Learn to Be Your Own Boss

### Set Work Boundaries

This is the most important lesson I learned. The biggest problem with working from home is the lack of boundaries between work and life.

**Fixed work hours**: I now strictly work from 9 to 6, shutting down the computer when time's up. It was hard at first, always feeling there were unfinished tasks, but after persisting for a while, I found efficiency actually improved.

**Create rituals**: I change into "work clothes" (actually just a specific T-shirt) before starting work, and take a 15-minute walk after finishing. These small rituals help me distinguish between work and life states.

**Independent workspace**: I specifically cleared out a corner at home as a work area, only working there. I absolutely don't touch work stuff in sleeping areas.

### Learn to Focus on Key Points

I used to be dragged around by various trivial matters, busy all day but not accomplishing much important work.

Now I list 3 most important things every morning and prioritize completing these. Other things, I postpone if possible, outsource if possible.

**Batch process trivial matters**: I concentrate email replies, customer service, social media browsing into fixed afternoon time slots to avoid interrupting my focus time.

### Learn to Say No

This is really hard but very important.

Before, when people asked me to help with small features or participate in activities, I was always too embarrassed to refuse. Result: my own project progress kept getting delayed.

Now I've learned to politely decline things that don't help my goals. Time is the most precious resource and should be spent where it matters most.

## Physical and Mental Health: Don't Overdraw Yourself

### Sleep is Primary Productivity

I used to think sleeping was a waste of time, often staying up until 2-3 AM. Result: extremely low efficiency the next day, plus prone to errors.

Now I strictly ensure 7-8 hours of sleep daily. Although work time decreased, efficiency improved, and overall output is actually higher.

### Exercise Isn't a Waste of Time

I used to think exercise was a waste of time - better to write more code with that time.

Later I discovered that appropriate exercise makes my thinking clearer and stress lower. Now I run for 30 minutes daily or do some simple strength training.

During exercise, my brain empties and I often think of solutions to work problems.

### Regular Digital Detox

Every week I arrange one day to completely avoid computers and phones, going hiking, watching movies, meeting with friends.

I was anxious at first, always worried about missing something important. But later I realized the world won't stop because I'm offline for a day.

This regular "digital detox" lets me re-examine my work and life, maintaining a clear mind.

## Some Practical Tips

### Use Tools to Manage Time

I use Toggl to track time, discovering where I spend too much time. I use Notion to manage tasks and project progress.

### Establish Feedback Loops

Every week I review the week's work and life, seeing what I did well and what needs improvement.

### Find Your Own Rhythm

Everyone's biological clock is different. I found I'm most efficient in the morning, so I schedule the most important work then. Afternoons are for handling relatively simple tasks.

## Final Thoughts

Going from workaholic to relative balance took me over a year. Now while I can't say it's completely balanced, at least I no longer neglect health and life because of work.

Most importantly, recognize that indie development is a marathon, not a sprint. Maintaining a sustainable pace is more important than short-term bursts.

If you're experiencing similar troubles, don't rush, adjust slowly. Everyone's situation is different - finding what works for you is most important.

Hope my experience helps you. If you have good methods, welcome to share and exchange!
