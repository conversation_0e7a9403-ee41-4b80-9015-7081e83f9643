{"title": "About Us - Frontend Developer Blog Sharing JavaScript & React Experience", "subtitle": "Learn more about Lafu Code", "authorName": "Lafu Code", "introduction": "Welcome to Lafu Code! I'm a passionate developer focused on sharing practical programming tips and development experience.", "skills": {"title": "Skills & Expertise", "frontend": {"title": "Frontend Development", "items": ["React / Next.js", "TypeScript / JavaScript", "Tailwind CSS / CSS3"]}, "backend": {"title": "Backend Development", "items": ["Node.js / Express", "Python / Django", "Database Design & Optimization"]}, "mobile": {"title": "Mobile Development", "items": ["React Native", "Flutter", "iOS / Swift", "Android / Kotlin", "Cross-platform Solutions"]}, "tools": {"title": "Development Tools", "items": ["Git / GitHub", "<PERSON><PERSON> / <PERSON>es", "CI/CD Pipelines", "Cloud Service Deployment"]}, "project": {"title": "Project Experience", "items": ["Large-scale Web Application Development", "Microservices Architecture Design", "Open Source Project Contributions"]}}, "philosophy": {"title": "Development Philosophy", "practical": {"title": "Practical First", "description": "Focus on sharing practical programming tips and solutions"}, "accessible": {"title": "Easy to Understand", "description": "Explain complex technical concepts in simple and clear ways"}, "community": {"title": "Community Sharing", "description": "Share knowledge with the developer community and grow together"}, "learning": {"title": "Continuous Learning", "description": "Maintain curiosity about new technologies and keep learning and exploring"}, "communication": {"title": "Effective Communication", "description": "Promote team collaboration through clear documentation and code comments"}}, "experience": {"title": "Work Experience", "description": "Years of software development experience, participated in the development and maintenance of multiple large-scale projects."}, "blog": {"title": "Blog Philosophy", "description": "By sharing technical articles and development experience, help more developers improve their skills and grow together."}, "techStack": {"title": "Tech Stack", "frontend": {"title": "Frontend Technologies"}, "backend": {"title": "Backend Technologies"}, "tools": {"title": "Development Tools"}}, "contact": {"title": "Contact", "description": "If you have any questions or suggestions, feel free to contact me!", "sendEmail": "Send Email", "github": "GitHub"}}