{"title": "🔍 Regex Tester Tool", "description": "Online regular expression testing and validation tool with real-time matching and syntax highlighting", "breadcrumb": {"home": "Home", "tools": "Toolbox", "current": "Regex Tester"}, "sections": {"pattern": "Regular Expression Pattern", "flags": "Flags", "commonPatterns": "Common Patterns", "testString": "Test String", "highlighted": "Highlighted Text", "matchDetails": "Match Details"}, "placeholders": {"pattern": "Enter regular expression...", "flags": "gimsuvy", "testString": "Enter text to test...", "highlightedEmpty": "Enter pattern and test string to see highlighted matches", "noMatches": "No matches found", "startTesting": "Enter a pattern and test string to start testing"}, "flags": {"label": "Flags:", "g": "Global (g)", "i": "Ignore case (i)", "m": "Multiline (m)", "s": "Dot all (s)", "u": "Unicode (u)", "y": "<PERSON>y (y)"}, "commonPatterns": {"email": "Email", "phone": "Phone", "idCard": "ID Card", "ip": "IP Address", "url": "URL", "chinese": "Chinese", "number": "Number", "letter": "Letter"}, "matchDetails": {"matchCount": "matches", "match": "Match", "position": "Position", "captureGroups": "Capture Groups", "empty": "(empty)"}, "errors": {"syntaxError": "Invalid regular expression syntax"}, "actions": {"clear": "Clear All"}, "tips": {"title": "Usage Tips", "metacharacters": {"title": "Metacharacters", "items": {"0": "<code>.</code> - Matches any character except newline", "1": "<code>^</code> - Matches start of string", "2": "<code>$</code> - Matches end of string", "3": "<code>*</code> - Matches 0 or more of the preceding element", "4": "<code>+</code> - Matches 1 or more of the preceding element", "5": "<code>?</code> - Matches 0 or 1 of the preceding element"}}, "characterClasses": {"title": "Character Classes", "items": {"0": "<code>\\d</code> - Matches any digit (0-9)", "1": "<code>\\w</code> - Matches any word character (a-z, A-Z, 0-9, _)", "2": "<code>\\s</code> - Matches any whitespace character", "3": "<code>[abc]</code> - Matches any character in the set", "4": "<code>[^abc]</code> - Matches any character not in the set"}}}, "meta": {"title": "Regex Tester Tool - Online Regular Expression Validator", "description": "Free online regular expression testing tool with real-time matching, syntax highlighting and capture group display."}}