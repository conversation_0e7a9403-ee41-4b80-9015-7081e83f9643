{"title": "🔗 WeChat and Work WeChat Auth URL Generator", "description": "Generate OAuth URLs for WeChat and Work WeChat", "breadcrumb": {"home": "Home", "tools": "Tools", "current": "WeChat Auth URL Generator"}, "authType": {"title": "Authorization Type", "wechat": "WeChat Official Account", "wechatDesc": "For WeChat Official Account web authorization", "workWechat": "Work WeChat", "workWechatDesc": "For internal enterprise application authorization"}, "config": {"title": "Configuration Parameters", "loadExample": "Load Example", "appId": "Application ID (AppID)", "corpId": "Corporation ID (CorpID)", "redirectUri": "Redirect URI", "agentId": "Agent ID", "scope": "Authorization Scope", "responseType": "Response Type", "state": "Custom State Parameter", "stateDesc": "Used to maintain request and callback state, returned as-is to the third party after authorization", "showAdvanced": "Show Advanced Settings", "hideAdvanced": "Hide Advanced Settings", "scopes": {"snsapi_base": "snsapi_base - Silent authorization, can get member basic info (UserId)", "snsapi_userinfo": "snsapi_userinfo - Pop-up authorization page, can get user basic info", "snsapi_privateinfo": "snsapi_privateinfo - Manual authorization, can get member detailed info including avatar, QR code and other sensitive data"}}, "result": {"title": "Generated Result", "generatedUrl": "Generated Authorization URL:", "placeholder": "Please fill in the required parameters to generate authorization URL"}, "actions": {"copy": "Copy URL", "clear": "Clear Form", "testUrl": "Test URL in New Window"}, "messages": {"copySuccess": "Authorization URL copied to clipboard!", "copyFailed": "Co<PERSON> failed, please copy the URL manually"}, "instructions": {"title": "Instructions", "step1": "Select authorization type: WeChat Official Account or Work WeChat", "step2": "Fill in the Application ID, redirect URI and other required parameters", "step3": "Choose appropriate authorization scope and other optional parameters", "step4": "Copy the generated authorization URL and integrate it into your application"}, "tips": {"title": "Important Tips", "wechat": {"security": "Ensure the redirect URI is properly configured in WeChat Official Account backend, otherwise authorization will fail", "redirect": "Redirect URI must use HTTPS protocol (except for local development)", "scope": "snsapi_userinfo requires manual user confirmation, snsapi_base is silent authorization", "state": "Recommend using state parameter to prevent CSRF attacks and ensure request security"}, "workWechat": {"security": "Ensure the redirect URI is properly configured in Work WeChat admin backend, otherwise authorization will fail", "redirect": "Redirect URI must use HTTPS protocol (except for local development)", "scope": "snsapi_privateinfo requires manual user confirmation, snsapi_base is silent authorization", "state": "Recommend using state parameter to prevent CSRF attacks and ensure request security", "agent": "Ensure the AgentID application is enabled and within visible scope"}}, "features": {"wechat": "WeChat Official Account authorization URL generation", "workWechat": "Work WeChat authorization URL generation", "customization": "Custom authorization parameters", "preview": "Real-time preview and testing"}, "meta": {"title": "WeChat Auth URL Generator - Free Online OAuth Link Builder", "description": "Free WeChat Official Account and Work WeChat OAuth authorization URL generator with custom parameter configuration for quick standard WeChat auth link generation.", "keywords": "WeChat authorization,<PERSON><PERSON><PERSON>,Official Account auth,Work WeChat,auth URL,WeChat development,web authorization"}}