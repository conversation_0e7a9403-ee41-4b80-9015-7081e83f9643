{"title": "🤖 AI Git Commit Message Generator", "description": "Intelligently generate standardized Git commit messages with multiple AI services and local generation support", "breadcrumb": {"home": "Home", "tools": "Tools", "current": "AI Git Commit Message Generator"}, "features": {"title": "Key Features", "aiGeneration": "AI Smart Generation", "localGeneration": "Local Rule Generation", "multiProvider": "Multiple AI Provider Support", "oneClick": "One-Click Operation", "smartFallback": "Smart Fallback Mechanism", "multiLanguage": "Multi-language Support", "multiStyle": "Multiple Commit Styles", "privacy": "Privacy Protection", "free": "Completely Free", "offline": "Offline Available"}, "providers": {"title": "Supported AI Services", "openai": "OpenAI (GPT-3.5/GPT-4)", "claude": "Anthropic <PERSON>", "gemini": "Google Gemini", "tongyi": "Alibaba <PERSON>", "local": "Local Rule Generation"}, "usage": {"title": "How to Use", "step1": "Modify code in Git repository", "step2": "Run git add . to stage changes", "step3": "Open VS Code source control panel", "step4": "Click ✨ button to generate commit message", "step5": "Review and commit code"}, "installation": {"title": "Installation", "marketplace": "Search in VS Code Extension Marketplace", "searchTerm": "LaFu AI Git Commit", "github": "GitHub Open Source Project", "website": "Official Website"}, "install": {"button": "Install Extension"}, "source": {"button": "View Source"}, "cta": {"title": "🚀 Get Started Now", "description": "Experience AI-powered intelligent commit message generation and boost your development efficiency!", "button": "Install Extension for Free"}, "meta": {"title": "AI Git Commit Message Generator - VS Code Extension", "description": "VS Code extension for intelligently generating standardized Git commit messages, supporting OpenAI, Claude, Gemini, <PERSON>yi <PERSON>wen and other AI services, with local generation and smart fallback mechanism, completely free to use.", "keywords": "Git commit message,AI generation,VS Code extension,OpenAI,Claude,Gemini,<PERSON>yi <PERSON>,development tools,code commit,automation"}}