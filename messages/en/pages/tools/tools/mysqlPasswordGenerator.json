{"meta": {"title": "MySQL Password Generator - Secure Strong Password Tool", "description": "Professional MySQL database password generator with customizable length and character types. Generate secure passwords that meet security standards to protect your database.", "keywords": "MySQL password generator,database password,strong password generator,secure password,random password,password tool"}, "title": "MySQL Password Generator", "description": "Generate secure and reliable MySQL database passwords with customizable length and character types", "breadcrumb": {"home": "Home", "tools": "Tools", "current": "MySQL Password Generator"}, "settings": {"title": "Password Settings", "length": "Password Length", "uppercase": "Include Uppercase Letters (A-Z)", "lowercase": "Include Lowercase Letters (a-z)", "numbers": "Include Numbers (0-9)", "symbols": "Include Symbols (!@#$%^&*)", "excludeSimilar": "Exclude Similar Characters (il1Lo0O)", "excludeAmbiguous": "Exclude Ambiguous Characters (braces, brackets, parentheses etc.)"}, "controls": {"generate": "Generate Password", "copy": "Copy"}, "result": {"title": "Generated Password", "strength": "Password Strength"}, "strength": {"weak": "Weak", "medium": "Medium", "strong": "Strong", "veryStrong": "Very Strong"}, "messages": {"noCharsetSelected": "Please select at least one character type!", "copied": "Password copied to clipboard!"}, "tips": {"title": "MySQL Password Security Tips", "tip1": "Use passwords with at least 12 characters, including uppercase, lowercase, numbers, and symbols", "tip2": "Avoid dictionary words, personal information, or common password patterns", "tip3": "Change database passwords regularly, recommended every 3-6 months", "tip4": "Use different passwords for different database environments", "tip5": "Use a password manager to securely store generated passwords"}}