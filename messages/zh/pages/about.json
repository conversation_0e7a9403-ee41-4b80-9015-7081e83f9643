{"title": "关于我们 - 专注前端开发的程序员博客，分享JavaScript、React技术经验", "subtitle": "了解更多关于老夫撸代码的信息", "authorName": "老夫撸代码", "introduction": "欢迎来到老夫撸代码！我是一名热爱编程的开发者，专注于分享实用的编程技巧和开发经验。", "skills": {"title": "技能专长", "frontend": {"title": "前端开发", "items": ["React / Next.js", "TypeScript / JavaScript", "Tailwind CSS / CSS3"]}, "backend": {"title": "后端开发", "items": ["Node.js / Express", "Python / Django", "数据库设计与优化"]}, "mobile": {"title": "移动开发", "items": ["React Native", "Flutter", "iOS / Swift", "Android / Kotlin", "跨平台解决方案"]}, "tools": {"title": "开发工具", "items": ["Git / GitHub", "<PERSON><PERSON> / <PERSON>es", "CI/CD 流水线", "云服务部署"]}, "project": {"title": "项目经验", "items": ["大型Web应用开发", "微服务架构设计", "开源项目贡献"]}}, "philosophy": {"title": "开发理念", "practical": {"title": "实用至上", "description": "专注于分享实用的编程技巧和解决方案"}, "accessible": {"title": "易于理解", "description": "用简单明了的方式解释复杂的技术概念"}, "community": {"title": "社区共享", "description": "与开发者社区分享知识，共同成长进步"}, "learning": {"title": "持续学习", "description": "保持对新技术的好奇心，不断学习和探索"}, "communication": {"title": "有效沟通", "description": "通过清晰的文档和代码注释促进团队协作"}}, "experience": {"title": "工作经验", "description": "拥有多年的软件开发经验，参与过多个大型项目的开发和维护。"}, "blog": {"title": "博客理念", "description": "通过分享技术文章和开发经验，帮助更多开发者提升技能，共同成长。"}, "techStack": {"title": "技术栈", "frontend": {"title": "前端技术"}, "backend": {"title": "后端技术"}, "tools": {"title": "开发工具"}}, "contact": {"title": "联系方式", "description": "如果您有任何问题或建议，欢迎与我联系！", "sendEmail": "发送邮件", "github": "GitHub"}}