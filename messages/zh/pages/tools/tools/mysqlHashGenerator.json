{"meta": {"title": "MySQL密码哈希生成器 - 在线MySQL哈希工具", "description": "世界上最简单的在线MySQL密码哈希生成器，专为Web开发者和程序员打造。支持PASSWORD()、OLD_PASSWORD()、SHA1、SHA256、MD5等多种哈希算法。", "keywords": "MySQL密码哈希,MySQL PASSWORD,哈希生成器,密码加密,数据库安全,在线工具"}, "title": "MySQL密码哈希生成器", "description": "将明文密码转换为MySQL哈希值，支持多种哈希算法", "breadcrumb": {"home": "首页", "tools": "在线工具", "current": "MySQL哈希生成器"}, "settings": {"title": "哈希算法选择", "password": "PASSWORD() - MySQL 4.1+", "oldPassword": "OLD_PASSWORD() - MySQL 4.1之前"}, "input": {"title": "输入密码", "password": "明文密码", "placeholder": "请输入要生成哈希的密码..."}, "output": {"title": "生成的哈希值", "hash": "哈希值"}, "controls": {"generate": "生成哈希", "copy": "复制", "clear": "清空"}, "examples": {"title": "使用示例", "createUser": "创建用户", "setPassword": "设置密码"}, "messages": {"emptyPassword": "请输入密码！", "copied": "哈希值已复制到剪贴板！"}, "tips": {"title": "安全提示", "tip1": "PASSWORD()函数在MySQL 5.7.6中已被弃用，建议使用更安全的认证插件", "tip2": "OLD_PASSWORD()仅用于兼容MySQL 4.1之前的版本，安全性较低", "tip3": "生成的哈希值可直接用于MySQL用户管理和密码设置", "tip4": "请确保在安全环境中使用此工具，避免在公共网络中处理敏感密码"}}