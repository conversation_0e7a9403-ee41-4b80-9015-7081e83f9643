{"title": "🔐 Base64 编码解码工具", "description": "在线Base64编码和解码工具，支持文本和文件处理", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "Base64 编码解码"}, "modes": {"encode": "编码", "decode": "解码"}, "controls": {"mode": "处理模式", "encode": "编码", "decode": "解码", "swap": "交换", "clear": "清空"}, "input": {"label": "输入内容", "placeholder": "请输入要处理的文本内容..."}, "output": {"label": "输出结果", "placeholder": "处理结果将显示在这里..."}, "actions": {"process": "处理", "clear": "清空", "copy": "复制结果"}, "messages": {"copySuccess": "结果已复制到剪贴板！", "copyFailed": "复制失败，请手动复制", "invalidInput": "输入内容无效，请检查后重试", "emptyInput": "请输入要处理的内容", "copied": "已复制到剪贴板！"}, "errors": {"encodeFailed": "编码失败，请检查输入内容", "decodeFailed": "解码失败，请检查Base64格式是否正确"}, "sections": {"originalText": "原始文本", "base64Encode": "Base64编码", "decodeResult": "解码结果", "characters": "字符", "copy": "复制"}, "placeholders": {"encodeInput": "请输入要编码的文本内容...", "decodeInput": "请输入要解码的Base64内容...", "encodeOutput": "编码结果将显示在这里...", "decodeOutput": "解码结果将显示在这里..."}, "tips": {"title": "使用提示", "items": {"0": "Base64是一种基于64个可打印字符来表示二进制数据的表示方法", "1": "常用于在HTTP环境下传递较长的标识信息", "2": "编码后的数据比原始数据略长（约4/3倍）", "3": "Base64编码是可逆的，可以通过解码还原原始数据", "4": "支持中文等Unicode字符的编码和解码"}}, "features": {"title": "功能特点", "encode": "文本Base64编码", "decode": "Base64解码还原", "realtime": "实时处理", "copy": "一键复制结果"}, "instructions": {"title": "使用说明", "encode": "编码：将普通文本转换为Base64格式", "decode": "解码：将Base64格式还原为普通文本", "step1": "选择编码或解码模式", "step2": "输入要处理的内容", "step3": "点击处理按钮或实时查看结果", "step4": "复制处理结果到剪贴板"}, "meta": {"title": "Base64编码解码工具 - 免费在线Base64转换器", "description": "免费的在线Base64编码解码工具，支持文本的Base64编码和解码转换，操作简单，结果准确。"}}