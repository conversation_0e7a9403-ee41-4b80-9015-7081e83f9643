{"title": "🌐 IP地址查询工具", "description": "在线IP地址查询工具，支持IPv4和IPv6地址的地理位置、ISP信息查询", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "IP地址查询"}, "input": {"label": "IP地址", "placeholder": "请输入IP地址，如：******* 或 2001:4860:4860::8888"}, "actions": {"lookup": "查询", "clear": "清空", "getCurrentIP": "获取当前IP", "getLocalExitIP": "获取本地出口IP"}, "results": {"title": "查询结果", "ip": "IP地址", "country": "国家", "region": "地区", "city": "城市", "isp": "ISP提供商", "timezone": "时区", "latitude": "纬度", "longitude": "经度", "asn": "ASN", "organization": "组织"}, "status": {"querying": "查询中...", "enterIP": "请输入IP地址", "invalidIP": "请输入有效的IP地址", "queryFailed": "查询失败，请稍后重试", "error": "查询失败，请稍后重试"}, "ipInfo": {"title": "IP地址信息", "currentExitIP": "当前网络出口IP地址", "currentExitIPDesc": "这是您当前网络环境的出口IP地址", "realIP": "检测到的真实IP地址", "realIPDesc": "检测到的真实IP地址", "vpnWarning": "⚠️ 检测到IP地址不一致，您可能正在使用VPN或代理服务"}, "details": {"title": "IP详细信息", "ipAddress": "IP地址", "location": "地理位置", "ispInfo": "ISP信息", "organization": "组织", "timezone": "时区", "coordinates": "坐标", "asInfo": "AS信息", "mapLink": "地图链接", "viewOnGoogleMaps": "在Google地图中查看"}, "errors": {"ipLookupFailed": "IP查询失败"}, "features": {"title": "功能特点", "ipv4": "支持IPv4地址查询", "ipv6": "支持IPv6地址查询", "location": "地理位置信息", "isp": "ISP提供商信息", "realtime": "实时查询", "privacy": "本地处理，保护隐私", "privacyTitle": "隐私安全", "privacyDesc": "查询过程不记录任何信息", "fastTitle": "快速查询", "fastDesc": "实时获取最新的IP信息", "globalTitle": "全球覆盖", "globalDesc": "支持全球IPv4和IPv6地址"}, "instructions": {"title": "使用说明", "step1": "输入要查询的IP地址", "step2": "点击查询按钮获取信息", "step3": "查看详细的地理位置和ISP信息", "step4": "支持IPv4和IPv6两种格式"}, "meta": {"title": "IP地址查询工具 - 免费在线IP位置查询", "description": "免费的在线IP地址查询工具，支持IPv4和IPv6地址的地理位置、ISP信息查询，实时准确。"}}