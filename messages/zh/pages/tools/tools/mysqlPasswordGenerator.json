{"meta": {"title": "MySQL密码生成器 - 安全强密码生成工具", "description": "专业的MySQL数据库密码生成器，支持自定义长度、字符类型，生成符合安全标准的强密码，保护您的数据库安全。", "keywords": "MySQL密码生成器,数据库密码,强密码生成,安全密码,随机密码,密码工具"}, "title": "MySQL密码生成器", "description": "生成安全可靠的MySQL数据库密码，支持自定义长度和字符类型", "breadcrumb": {"home": "首页", "tools": "在线工具", "current": "MySQL密码生成器"}, "settings": {"title": "密码设置", "length": "密码长度", "uppercase": "包含大写字母 (A-Z)", "lowercase": "包含小写字母 (a-z)", "numbers": "包含数字 (0-9)", "symbols": "包含特殊符号 (!@#$%^&*)", "excludeSimilar": "排除相似字符 (il1Lo0O)", "excludeAmbiguous": "排除易混淆字符 (大括号方括号圆括号等)"}, "controls": {"generate": "生成密码", "copy": "复制"}, "result": {"title": "生成的密码", "strength": "密码强度"}, "strength": {"weak": "弱", "medium": "中等", "strong": "强", "veryStrong": "非常强"}, "messages": {"noCharsetSelected": "请至少选择一种字符类型！", "copied": "密码已复制到剪贴板！"}, "tips": {"title": "MySQL密码安全建议", "tip1": "使用至少12位字符的密码，包含大小写字母、数字和特殊符号", "tip2": "避免使用字典词汇、个人信息或常见密码模式", "tip3": "定期更换数据库密码，建议每3-6个月更换一次", "tip4": "为不同的数据库环境使用不同的密码", "tip5": "使用密码管理器安全存储生成的密码"}}