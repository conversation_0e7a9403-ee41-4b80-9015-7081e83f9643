# Giscus评论系统配置指南

## 概述

Giscus是一个基于GitHub Discussions的评论系统，可以为您的博客提供强大的评论功能。本指南将帮助您完成完整的配置过程。

## 🚨 当前状态

目前系统使用Giscus官方仓库作为临时方案。为了获得最佳体验，强烈建议您配置自己的评论仓库。

## 📋 配置步骤

### 步骤1: 创建GitHub仓库

1. **登录GitHub**，访问 [https://github.com/new](https://github.com/new)

2. **创建新仓库**：
   - Repository name: `blog-comments` (或您喜欢的名称)
   - Description: `Comments for my blog powered by Giscus`
   - 选择 **Public** (必须是公开仓库)
   - 勾选 **Add a README file**

3. **启用Discussions功能**：
   - 进入新创建的仓库
   - 点击 **Settings** 标签
   - 向下滚动到 **Features** 部分
   - 勾选 **Discussions** 复选框

### 步骤2: 安装Giscus App

1. **访问Giscus App页面**：[https://github.com/apps/giscus](https://github.com/apps/giscus)

2. **安装应用**：
   - 点击绿色的 **Install** 按钮
   - 选择安装到您的账户
   - 在仓库选择页面，选择 **Selected repositories**
   - 选择您刚创建的 `blog-comments` 仓库
   - 点击 **Install**

### 步骤3: 配置Giscus

1. **访问Giscus配置页面**：[https://giscus.app/](https://giscus.app/)

2. **输入仓库信息**：
   - 在 "Repository" 字段输入：`your-username/blog-comments`
   - 等待验证通过（显示绿色勾号）

3. **配置映射方式**：
   - 选择 **Discussion title contains page title**
   - 这样每篇文章会根据标题创建对应的讨论

4. **选择Discussion分类**：
   - 推荐选择 **General**
   - 或者在您的仓库中创建专门的 **Comments** 分类

5. **其他设置**：
   - Features: 勾选 **Enable reactions**
   - Theme: 选择 **Preferred color scheme**
   - Language: 选择 **中文 (简体)**

### 步骤4: 获取配置信息

配置完成后，页面底部会显示配置代码，类似：

```html
<script src="https://giscus.app/client.js"
        data-repo="your-username/blog-comments"
        data-repo-id="R_kgDOxxxxxxx"
        data-category="General"
        data-category-id="DIC_kwDOxxxxxxx"
        data-mapping="title"
        data-strict="0"
        data-reactions-enabled="1"
        data-emit-metadata="0"
        data-input-position="top"
        data-theme="preferred_color_scheme"
        data-lang="zh-CN"
        crossorigin="anonymous"
        async>
</script>
```

### 步骤5: 更新博客配置

1. **打开配置文件**：`src/lib/giscus-config.ts`

2. **替换配置信息**：
   ```typescript
   export const giscusConfig = {
     repo: "your-username/blog-comments", // 替换为您的仓库
     repoId: "R_kgDOxxxxxxx", // 从giscus.app获取
     category: "General", // 您选择的分类
     categoryId: "DIC_kwDOxxxxxxx", // 从giscus.app获取
     mapping: "title" as const,
     reactionsEnabled: "1" as const,
     emitMetadata: "0" as const,
     inputPosition: "top" as const,
     lang: "zh-CN" as const,
     loading: "lazy" as const,
   };
   ```

3. **保存文件**并重启开发服务器

## 🔧 高级配置

### 创建专门的评论分类

1. 进入您的评论仓库
2. 点击 **Discussions** 标签
3. 点击右侧的 **Categories** 
4. 点击 **New category**
5. 创建名为 "Comments" 的分类
6. 在giscus.app中重新配置，选择新创建的分类

### 自定义主题

您可以在 `src/app/_components/comments.tsx` 中自定义主题：

```typescript
theme={theme === "dark" ? "dark_dimmed" : "light"}
```

可用主题：
- `light` / `dark`
- `preferred_color_scheme`
- `light_high_contrast` / `dark_high_contrast`
- `light_protanopia` / `dark_protanopia`
- `light_tritanopia` / `dark_tritanopia`

## 🐛 常见问题

### 1. 404错误
- 确保仓库是公开的
- 确保已安装Giscus App
- 确保仓库名称正确

### 2. 评论不显示
- 检查仓库ID和分类ID是否正确
- 确保Discussions功能已启用
- 检查网络连接

### 3. 权限问题
- 确保Giscus App有访问仓库的权限
- 检查仓库的Discussions设置

## 📊 管理评论

### 查看评论
- 访问您的评论仓库
- 点击 **Discussions** 标签
- 查看所有文章的评论讨论

### 管理评论
- 作为仓库所有者，您可以：
  - 删除不当评论
  - 锁定讨论
  - 置顶重要讨论
  - 设置讨论分类

### 评论通知
- GitHub会自动发送评论通知到您的邮箱
- 您可以在GitHub设置中调整通知偏好

## 🔒 安全考虑

1. **仓库权限**：评论仓库应该是公开的，但不要在其中存储敏感信息
2. **评论审核**：定期检查评论内容，及时处理垃圾评论
3. **备份**：GitHub Discussions的数据可以通过API导出备份

## 📈 分析和统计

您可以通过以下方式分析评论数据：
- GitHub Insights查看仓库活动
- 使用GitHub API获取评论统计
- 集成第三方分析工具

完成配置后，您的博客就拥有了功能强大的评论系统！
